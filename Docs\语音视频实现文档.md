# 语音视频通话系统实现文档

## 概述

本项目采用 **声网(Agora)** 作为音视频通话的核心SDK，结合 **腾讯云IM** 实现完整的语音视频通话功能。

## 技术架构

### 1. 核心技术栈

- **音视频SDK**: 声网(Agora) RTC SDK
- **即时通讯**: 腾讯云IM (Instant Messaging)
- **前端**: AgoraRTC Web SDK 4.18.3
- **后端**: PHP + ThinkPHP框架
- **存储**: 腾讯云COS (录制文件存储)

### 2. 系统架构图

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   客户端APP     │    │   Web管理后台   │    │   服务端API     │
│                 │    │                 │    │                 │
│ AgoraRTC SDK    │◄──►│ AgoraRTC Web    │◄──►│ Agora REST API  │
│ 腾讯IM SDK      │    │ 监控界面        │    │ 腾讯IM REST API │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │   声网云服务    │
                    │ + 腾讯云IM服务  │
                    │ + 腾讯云COS     │
                    └─────────────────┘
```

## 声网(Agora)集成

### 1. SDK文件结构

```
demo/public/system/agora/
├── src/
│   ├── AccessToken.php          # Token访问控制
│   ├── DynamicKey5.php         # 动态密钥生成
│   ├── RtcTokenBuilder.php     # RTC Token构建器
│   └── SignalingToken.php      # 信令Token
├── AgoraApi.php                # 声网API封装类
└── agora_api.php              # 声网API函数集合
```

### 2. 配置参数

在系统配置中需要设置以下参数：

```php
// 声网配置
$config = [
    'app_agora_key'     => 'YOUR_AGORA_APP_ID',        // 声网应用ID
    'app_certificate'   => 'YOUR_AGORA_CERTIFICATE',   // 声网证书
];
```

### 3. Token生成

**核心函数**: `get_agora_token($uid, $channelName)`

```php
/**
 * 获取声网token
 * @param integer $uid         用户ID
 * @param string  $channelName 频道ID
 */
function get_agora_token($uid, $channelName)
{
    require_once DOCUMENT_ROOT . '/system/agora/src/RtcTokenBuilder.php';
    
    $config = load_cache('config');
    $appID = $config['app_agora_key'];
    $appCertificate = $config['app_certificate'];
    
    if (empty($appID) || empty($appCertificate)) {
        return '';
    }
    
    $role = RtcTokenBuilder::RoleAttendee;
    $expireTimeInSeconds = 3600 * 24 * 30; // 30天有效期
    $currentTimestamp = (new DateTime("now", new DateTimeZone('UTC')))->getTimestamp();
    $privilegeExpiredTs = $currentTimestamp + $expireTimeInSeconds;
    
    $token = RtcTokenBuilder::buildTokenWithUid($appID, $appCertificate, $channelName, $uid, $role, $privilegeExpiredTs);
    
    return $token;
}
```

### 4. 通话流程

#### 4.1 发起通话

**接口**: `VideoCallApi::video_call_1215()`

```php
// 1. 生成通话频道ID
$channel_id = NOW_TIME . $this->param_uid . mt_rand(1000, 9999);

// 2. 生成双方Token
$returnData['pano_token'] = get_agora_token($this->param_uid, $channel_id);
$returnData['to_pano_token'] = get_agora_token($id, $channel_id);

// 3. 创建通话记录
$call_record = [
    'user_id'        => $this->param_uid,
    'call_be_user_id'=> $id,
    'channel_id'     => $channel_id,
    'status'         => 0,
    'type'           => $call_type, // 1=视频 2=语音
    'create_time'    => NOW_TIME,
];
```

#### 4.2 前端集成

**Web端**: 使用 AgoraRTC Web SDK

```javascript
// 创建Agora客户端
var client = AgoraRTC.createClient({ mode: "rtc", codec: "vp8" });

// 客户端配置
var options = {
  appid: null,
  channel: null,
  uid: null,
  token: null
};

// 加入频道
client.join(options.token, options.channel, options.uid);
```

## 腾讯云IM集成

### 1. SDK文件结构

```
demo/public/system/tim/
├── TimApi.php              # IM API入口
├── TimRestApi.php          # REST API封装
└── TimRestApiGear.php      # API工具类
```

### 2. 配置参数

```php
// 腾讯IM配置
$config = [
    'tencent_sdkappid'      => 'YOUR_SDKAPPID',        // 应用ID
    'tencent_identifier'    => 'administrator',        // 管理员账号
    'tencent_sha_key'       => 'YOUR_SECRET_KEY',      // 密钥
    'tencent_group_id'      => '@FGC#FULLGROUPIDKEY',  // 全局群组ID
];
```

### 3. 核心功能

#### 3.1 消息发送

```php
// 发送文本消息
function send_c2c_text_msg($user_id, $to_user_id, $msg)
{
    require_once(DOCUMENT_ROOT . '/system/tim/TimApi.php');
    $api = createTimAPI();
    $api->openim_send_msg($user_id, $to_user_id, $msg);
}

// 发送自定义消息
function send_im_custom_msg($user_id, $to_user_id, $type, $data = [], $desc = '')
{
    $ext = [
        'type' => $type,
        'sender' => ['id' => $user_id]
    ];
    
    if (!empty($data) && is_array($data)) {
        $ext = array_merge($ext, $data);
    }
    
    $msgBody = [
        [
            'MsgType' => 'TIMCustomElem',
            'MsgContent' => [
                'Data' => json_encode($ext),
                'Desc' => $desc,
            ]
        ]
    ];
    
    $imService = get_im_service_instance();
    return $imService->sendCustomMessage($user_id, $to_user_id, $msgBody);
}
```

#### 3.2 群组管理

```php
// 创建群组
function qcloud_group_create_group($group_type = 'AVChatRoom', $user_id, $user_id1, $video_id)
{
    $api = set_qcloud_user_sig();
    $ret = $api->group_create_group($group_type, $user_id, (string)$user_id1, $video_id);
    return $ret;
}

// 销毁群组
function qcloud_group_destroy_group($group_id)
{
    $api = set_qcloud_user_sig();
    $ret = $api->group_destroy_group((string)$group_id);
    return $ret;
}
```

## 通话监控系统

### 1. 管理后台监控

**文件位置**: `demo/public/themes/admin_simpleboot3/admin/video_call_list/`

- `video_call_list.html` - 通话列表页面
- `select_video.html` - 通话监控页面

### 2. 实时监控功能

```javascript
// 监控页面使用AgoraRTC Web SDK
<script src="/static/js/AgoraRTC_N-4.18.3.js"></script>
<script src="/static/js/index.js"></script>

// 监控提示
<div class="alert alert-danger">
    <strong>提示!</strong> 监控大概延迟15-20s
</div>
```

## 录制与存储

### 1. 云端录制

使用声网云端录制服务，录制文件存储到腾讯云COS：

```php
// 录制配置
$bodyArray = [
    'uid' => $recordUid,
    'cname' => $channelId,
    'clientRequest' => [
        'token' => $token,
        'recordingConfig' => [
            'transcodingConfig' => [
                'height' => 360,
                'width' => 640,
                'bitrate' => 500,
                'fps' => 15,
                'mixedVideoLayout' => 1
            ]
        ],
        'recordingFileConfig' => [
            'avFileType' => ['hls', 'mp4'],
        ],
        'storageConfig' => [
            'accessKey' => $ten_info['secret_id'],
            'secretKey' => $ten_info['secret_key'],
            'region' => 2,
            'vendor' => 3, // 腾讯云存储
            'bucket' => $ten_info['bucket'],
            'fileNamePrefix' => ['videoRecord', $channelId]
        ]
    ]
];
```

### 2. 推流配置

支持RTMP推流到直播平台：

```php
// 推流配置
$bodyArray = [
    'converter' => [
        'transcodeOptions' => [
            'rtcChannel' => (string)$channelName,
            'videoOptions' => [
                'canvas' => [
                    'width' => 720,
                    'height' => 1280,
                ],
                'bitrate' => 400,
                'layoutType' => 0,
            ],
            'audioOptions' => [
                'volumes'=> [
                    ['volume'=> 80, 'rtcStreamUid' => $uid1],
                    ['volume'=> 100, 'rtcStreamUid' => $uid2]
                ]
            ]
        ],
        'rtmpUrl' => $pushUrl,
    ]
];
```

## 数据库设计

### 1. 通话记录表

```sql
CREATE TABLE `video_call_record` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '发起用户ID',
  `call_be_user_id` int(11) NOT NULL COMMENT '被叫用户ID',
  `channel_id` varchar(100) NOT NULL COMMENT '通话频道ID',
  `status` tinyint(1) DEFAULT '0' COMMENT '通话状态 0=发起 1=接通 2=挂断',
  `type` tinyint(1) DEFAULT '1' COMMENT '通话类型 1=视频 2=语音',
  `create_time` int(11) NOT NULL COMMENT '创建时间',
  `update_time` int(11) DEFAULT NULL COMMENT '更新时间',
  `duration` int(11) DEFAULT '0' COMMENT '通话时长(秒)',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_call_be_user_id` (`call_be_user_id`),
  KEY `idx_channel_id` (`channel_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='视频通话记录表';
```

## 部署配置

### 1. 声网配置

1. 注册声网开发者账号
2. 创建项目获取 App ID 和 App Certificate
3. 配置到系统设置中

### 2. 腾讯云IM配置

1. 开通腾讯云IM服务
2. 获取 SDKAppID 和密钥
3. 创建管理员账号
4. 配置全局群组

### 3. 存储配置

1. 开通腾讯云COS服务
2. 创建存储桶
3. 配置访问密钥

## 常见问题

### 1. Token过期问题

- Token有效期设置为30天
- 建议在客户端实现Token刷新机制

### 2. 网络连接问题

- 确保防火墙开放相关端口
- 检查域名解析是否正常

### 3. 录制文件问题

- 检查存储配置是否正确
- 确认存储桶权限设置

## API接口文档

### 1. 通话相关接口

#### 1.1 发起通话

**接口地址**: `POST /api/v5/video_call/video_call_1215`

**请求参数**:
```json
{
    "id": 123,              // 被叫用户ID
    "call_type": 1,         // 通话类型 1=视频 2=语音
    "os": "iOS",            // 操作系统
    "brand": "iPhone",      // 设备品牌
    "model": "iPhone12",    // 设备型号
    "app_version": "1.0.0", // APP版本
    "rtc_sdk": "agora"      // RTC SDK类型
}
```

**响应数据**:
```json
{
    "code": 1,
    "msg": "success",
    "data": {
        "channel_id": "1640995200123456789",
        "pano_token": "agora_token_string",
        "to_pano_token": "agora_token_string_for_receiver",
        "charge_info": {
            "video_price": 10,
            "voice_price": 5
        }
    }
}
```

#### 1.2 检查通话状态

**接口地址**: `POST /api/v5/video_call/check_video_call`

**请求参数**:
```json
{
    "to_user_id": 123
}
```

**响应数据**:
```json
{
    "code": 1,
    "msg": "success",
    "data": {
        "is_call_record": 1  // 0=无通话 1=有通话
    }
}
```

#### 1.3 开始推流

**接口地址**: `POST /api/v5/video_call/start_conversation_streaming`

**请求参数**:
```json
{
    "channel_id": "1640995200123456789",
    "rtc_sdk": "agora"
}
```

### 2. IM消息接口

#### 2.1 发送文本消息

**函数**: `send_im_text_msg($user_id, $to_user_id, $msg)`

#### 2.2 发送自定义消息

**函数**: `send_im_custom_msg($user_id, $to_user_id, $type, $data, $desc)`

#### 2.3 发送全局广播

**函数**: `send_global_gift_message($send_user_info, $to_user_info, $count, $gift_name, $gift_icon, $gift_coin)`

## 消息类型定义

### 1. IM消息类型枚举

```php
class ImMessageEnum
{
    const TEXT_MESSAGE = 1;           // 文本消息
    const IMAGE_MESSAGE = 2;          // 图片消息
    const VOICE_MESSAGE = 3;          // 语音消息
    const VIDEO_MESSAGE = 4;          // 视频消息
    const GIFT_MESSAGE = 5;           // 礼物消息
    const GLOBAL_GIFT = 777;          // 全局礼物广播
    const VIDEO_CALL_INVITE = 10;     // 视频通话邀请
    const VIDEO_CALL_ACCEPT = 11;     // 接受通话
    const VIDEO_CALL_REJECT = 12;     // 拒绝通话
    const VIDEO_CALL_HANGUP = 13;     // 挂断通话
    const BROADCST_FLOATING_SCREEN = 20; // 全局广播飘屏
}
```

### 2. 通话状态定义

```php
class CallStatusEnum
{
    const INITIATED = 0;    // 发起通话
    const CONNECTED = 1;    // 通话接通
    const ENDED = 2;        // 通话结束
    const REJECTED = 3;     // 通话被拒绝
    const TIMEOUT = 4;      // 通话超时
}
```

## 错误处理

### 1. 常见错误码

```php
const ERROR_CODES = [
    10001 => '用户不存在',
    10002 => '余额不足',
    10003 => '用户忙线中',
    10004 => '通话已存在',
    10005 => 'Token生成失败',
    10006 => '频道创建失败',
    10007 => '录制启动失败',
    20001 => 'IM服务异常',
    20002 => '群组不存在',
    20003 => '消息发送失败',
];
```

### 2. 异常处理示例

```php
try {
    $result = VideoCallLogic::startCall($params);
    if ($result === false) {
        return JsonService::fail(VideoCallLogic::getError());
    }
    return JsonService::success('通话发起成功', $result);
} catch (\Exception $e) {
    LogService::write('通话发起异常', [
        'params' => $params,
        'error' => $e->getMessage()
    ], 'video_call_error');
    return JsonService::fail('系统异常，请稍后重试');
}
```

## 性能优化

### 1. 缓存策略

- 用户信息缓存：Redis缓存用户基本信息，减少数据库查询
- Token缓存：缓存生成的Agora Token，避免重复生成
- 配置缓存：系统配置信息缓存，提高响应速度

### 2. 数据库优化

- 通话记录表添加索引：用户ID、频道ID、创建时间
- 分表策略：按月份分表存储通话记录
- 读写分离：读操作使用从库，写操作使用主库

### 3. 网络优化

- CDN加速：静态资源使用CDN分发
- 就近接入：选择最近的声网节点
- 网络质量监控：实时监控网络质量，自动调整码率

## 扩展功能

### 1. 多人通话

可扩展支持多人音视频通话，需要调整频道管理和UI布局。

### 2. 屏幕共享

集成屏幕共享功能，适用于在线教育等场景。

### 3. 美颜滤镜

集成第三方美颜SDK，提升用户体验。

### 4. 通话质量分析

- 集成声网质量监控
- 实时网络质量检测
- 通话质量评分系统

---

**注意**: 本文档基于当前项目代码分析整理，实际部署时请根据具体需求调整配置参数。
