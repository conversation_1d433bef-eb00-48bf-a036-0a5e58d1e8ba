# 视频通话实时费用监控功能文档

## 功能概述

实现用户A给用户B打视频通话时的实时费用监控功能：
- 用户A（主叫方）：实时查看花费余额、剩余余额、余额是否足够
- 用户B（被叫方）：实时查看当前收益（预计）
- 只有挂断后才会实际扣费和结算收益

## API接口

### 1. 获取通话实时费用信息

**接口地址：** `POST /applent/video/getCallCostInfo`

**请求参数：**
```json
{
    "channel_id": "通话频道ID"
}
```

**响应示例：**
```json
{
    "code": 1,
    "msg": "获取成功",
    "data": {
        "is_connected": true,
        "call_info": {
            "call_id": 123,
            "channel_id": "ch_123456",
            "duration": 125,
            "duration_minutes": 3,
            "type": 1,
            "start_time": "2025-01-01 10:00:00"
        },
        "cost_info": {
            "price_per_minute": 10.00,
            "total_cost": 30.00,
            "platform_rate": 0.3
        },
        "caller_info": {
            "user_id": 1001,
            "nickname": "用户A",
            "original_balance": 100.00,
            "remaining_balance": 70.00,
            "is_balance_sufficient": true,
            "warning_threshold": 20.00
        },
        "callee_info": {
            "user_id": 1002,
            "nickname": "用户B", 
            "current_earnings": 21.00,
            "earnings_per_minute": 7.00
        },
        "warnings": [
            {
                "type": "info",
                "message": "余额较低，建议及时充值"
            }
        ]
    }
}
```

**字段说明：**
- `is_connected`: 通话是否已连接
- `duration`: 通话时长（秒）
- `duration_minutes`: 通话时长（分钟，向上取整）
- `price_per_minute`: 每分钟价格
- `total_cost`: 已产生总费用（预计）
- `remaining_balance`: 主叫方剩余余额
- `current_earnings`: 被叫方当前预计收益
- `warnings`: 余额警告信息

## 前端实现示例

### JavaScript轮询实现

```javascript
class VideoCallCostMonitor {
    constructor(channelId, userId, userType) {
        this.channelId = channelId;
        this.userId = userId;
        this.userType = userType; // 'caller' 或 'callee'
        this.pollInterval = null;
        this.pollFrequency = 3000; // 3秒轮询一次
    }
    
    // 开始监控
    startMonitoring() {
        this.pollInterval = setInterval(() => {
            this.fetchCostInfo();
        }, this.pollFrequency);
        
        // 立即执行一次
        this.fetchCostInfo();
    }
    
    // 停止监控
    stopMonitoring() {
        if (this.pollInterval) {
            clearInterval(this.pollInterval);
            this.pollInterval = null;
        }
    }
    
    // 获取费用信息
    async fetchCostInfo() {
        try {
            const response = await fetch('/applent/video/getCallCostInfo', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': 'Bearer ' + this.getToken()
                },
                body: JSON.stringify({
                    channel_id: this.channelId
                })
            });
            
            const result = await response.json();
            
            if (result.code === 1) {
                this.updateUI(result.data);
            } else {
                console.error('获取费用信息失败:', result.msg);
            }
        } catch (error) {
            console.error('网络请求失败:', error);
        }
    }
    
    // 更新UI显示
    updateUI(data) {
        if (!data.is_connected) {
            this.showDisconnectedState();
            return;
        }
        
        if (this.userType === 'caller') {
            this.updateCallerUI(data);
        } else {
            this.updateCalleeUI(data);
        }
        
        // 显示警告信息
        this.showWarnings(data.warnings);
    }
    
    // 更新主叫方UI
    updateCallerUI(data) {
        const { caller_info, cost_info, call_info } = data;
        
        // 更新通话时长
        document.getElementById('call-duration').textContent = 
            this.formatDuration(call_info.duration);
        
        // 更新费用信息
        document.getElementById('total-cost').textContent = 
            `¥${cost_info.total_cost.toFixed(2)}`;
        
        // 更新余额信息
        document.getElementById('remaining-balance').textContent = 
            `¥${caller_info.remaining_balance.toFixed(2)}`;
        
        // 余额状态指示
        const balanceStatus = document.getElementById('balance-status');
        if (caller_info.is_balance_sufficient) {
            balanceStatus.className = 'status-sufficient';
            balanceStatus.textContent = '余额充足';
        } else {
            balanceStatus.className = 'status-insufficient';
            balanceStatus.textContent = '余额不足';
        }
        
        // 更新进度条
        const balancePercent = (caller_info.remaining_balance / caller_info.original_balance) * 100;
        document.getElementById('balance-progress').style.width = `${balancePercent}%`;
    }
    
    // 更新被叫方UI
    updateCalleeUI(data) {
        const { callee_info, call_info } = data;
        
        // 更新通话时长
        document.getElementById('call-duration').textContent = 
            this.formatDuration(call_info.duration);
        
        // 更新收益信息
        document.getElementById('current-earnings').textContent = 
            `¥${callee_info.current_earnings.toFixed(2)}`;
        
        document.getElementById('earnings-per-minute').textContent = 
            `¥${callee_info.earnings_per_minute.toFixed(2)}/分钟`;
    }
    
    // 显示警告信息
    showWarnings(warnings) {
        const warningContainer = document.getElementById('warning-container');
        warningContainer.innerHTML = '';
        
        warnings.forEach(warning => {
            const warningEl = document.createElement('div');
            warningEl.className = `warning warning-${warning.type}`;
            warningEl.textContent = warning.message;
            warningContainer.appendChild(warningEl);
        });
    }
    
    // 格式化时长显示
    formatDuration(seconds) {
        const minutes = Math.floor(seconds / 60);
        const remainingSeconds = seconds % 60;
        return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
    }
    
    // 获取认证token
    getToken() {
        return localStorage.getItem('auth_token');
    }
}

// 使用示例
const costMonitor = new VideoCallCostMonitor('ch_123456', 1001, 'caller');

// 通话连接后开始监控
costMonitor.startMonitoring();

// 通话结束时停止监控
// costMonitor.stopMonitoring();
```

### HTML界面示例

#### 主叫方界面
```html
<div class="call-cost-panel caller-panel">
    <div class="call-info">
        <h3>通话中</h3>
        <div class="duration">通话时长: <span id="call-duration">00:00</span></div>
    </div>
    
    <div class="cost-info">
        <div class="cost-item">
            <label>已花费:</label>
            <span id="total-cost" class="cost-amount">¥0.00</span>
        </div>
        
        <div class="balance-info">
            <div class="balance-item">
                <label>剩余余额:</label>
                <span id="remaining-balance" class="balance-amount">¥0.00</span>
                <span id="balance-status" class="status-sufficient">余额充足</span>
            </div>
            
            <div class="balance-progress-bar">
                <div id="balance-progress" class="progress-fill"></div>
            </div>
        </div>
    </div>
    
    <div id="warning-container" class="warning-container"></div>
</div>
```

#### 被叫方界面
```html
<div class="call-cost-panel callee-panel">
    <div class="call-info">
        <h3>通话中</h3>
        <div class="duration">通话时长: <span id="call-duration">00:00</span></div>
    </div>
    
    <div class="earnings-info">
        <div class="earnings-item">
            <label>当前收益:</label>
            <span id="current-earnings" class="earnings-amount">¥0.00</span>
        </div>
        
        <div class="earnings-rate">
            <label>收益率:</label>
            <span id="earnings-per-minute">¥0.00/分钟</span>
        </div>
    </div>
    
    <div class="earnings-note">
        <small>* 收益将在通话结束后到账</small>
    </div>
</div>
```

## 配置说明

### 1. 平台抽成配置

在 `config/video_call.php` 中配置：

```php
return [
    'platform_rate' => 0.3, // 平台抽成30%
    'min_call_duration' => 60, // 最小计费时长（秒）
    'poll_frequency' => 3000, // 前端轮询频率（毫秒）
];
```

### 2. 路由配置

在 `route/applent.php` 中添加：

```php
// 视频通话相关路由
Route::group('video', function () {
    Route::post('start_call', 'video.VideoController/start_call');
    Route::post('acceptCall', 'video.VideoController/acceptCall');
    Route::post('rejectCall', 'video.VideoController/rejectCall');
    Route::post('hangupCall', 'video.VideoController/hangupCall');
    Route::post('getCallCostInfo', 'video.VideoController/getCallCostInfo'); // 新增
    Route::post('getAgoraToken', 'video.VideoController/getAgoraToken');
    Route::get('getAgoraConfig', 'video.VideoController/getAgoraConfig');
    Route::get('getCallRecords', 'video.VideoController/getCallRecords');
    Route::get('getCallStats', 'video.VideoController/getCallStats');
    Route::post('reportQuality', 'video.VideoController/reportQuality');
    Route::get('checkCallStatus', 'video.VideoController/checkCallStatus');
});
```

## 注意事项

1. **轮询频率**: 建议3-5秒轮询一次，避免过于频繁的请求
2. **余额不足处理**: 当余额不足时，前端应提示用户充值或自动结束通话
3. **网络异常处理**: 轮询失败时应有重试机制
4. **性能优化**: 可考虑使用WebSocket替代轮询以提高实时性
5. **数据缓存**: 可在Redis中缓存通话状态，减少数据库查询

## 测试建议

1. 测试余额充足时的正常计费流程
2. 测试余额不足时的处理逻辑
3. 测试网络中断时的重连机制
4. 测试长时间通话的性能表现
5. 测试并发通话的系统负载
