<?php

namespace app\applent\controller\video;

use app\applent\controller\BaseApiController;
use app\applent\logic\video\VideoCallLogic;
use app\applent\validate\video\VideoCallValidate;
use think\facade\Cache;

/**
 * 视频通话控制器
 */
class VideoController extends BaseApiController
{
    /**
     * 发起通话
     */
    public function start_call()
    {
        // 验证参数
        $params = (new VideoCallValidate())->post()->goCheck('start_call');

        // 添加用户ID和IP
        $params['user_id'] = $this->userId;
        $params['ip'] = $this->request->ip();

        $result = VideoCallLogic::startCall($params);
        if (false === $result) {
            return $this->fail(VideoCallLogic::getError());
        }

        return $this->success('通话发起成功', $result);
    }
    
    /**
     * 接受通话
     */
    public function acceptCall()
    {
        // 验证参数
        $params = (new VideoCallValidate())->post()->goCheck('accept_call');

        $result = VideoCallLogic::acceptCall($this->userId, $params['channel_id']);
        if (false === $result) {
            return $this->fail(VideoCallLogic::getError());
        }

        return $this->success('通话已接受', $result);
    }
    
    /**
     * 拒绝通话
     */
    public function rejectCall()
    {
        // 验证参数
        $params = (new VideoCallValidate())->post()->goCheck('reject_call');

        $result = VideoCallLogic::rejectCall($this->userId, $params['channel_id']);
        if (false === $result) {
            return $this->fail(VideoCallLogic::getError());
        }

        return $this->success('通话已拒绝', $result);
    }
    
    /**
     * 挂断通话
     */
    public function hangupCall()
    {
        // 验证参数
        $params = (new VideoCallValidate())->post()->goCheck('hangup_call');

        $result = VideoCallLogic::hangupCall($this->userId, $params['channel_id']);
        if (false === $result) {
            return $this->fail(VideoCallLogic::getError());
        }

        return $this->success('通话已结束', $result);
    }
    
    /**
     * 获取声网Token
     */
    public function getAgoraToken()
    {
        // 验证参数
        $params = (new VideoCallValidate())->post()->goCheck('get_token');

        $result = VideoCallLogic::getAgoraToken($this->userId, $params['channel_id'], $params['role'] ?? 'attendee');
        if (false === $result) {
            return $this->fail(VideoCallLogic::getError());
        }

        return $this->success('Token获取成功', $result);
    }
    
    /**
     * 获取声网配置
     */
    public function getAgoraConfig()
    {
        $result = VideoCallLogic::getAgoraConfig();
        if (false === $result) {
            return $this->fail(VideoCallLogic::getError());
        }

        return $this->success('配置获取成功', $result);
    }
    
    /**
     * 获取通话记录
     */
    public function getCallRecords()
    {
        // 验证参数
        $params = (new VideoCallValidate())->get()->goCheck('get_records');

        // 添加用户ID
        $params['user_id'] = $this->userId;

        $result = VideoCallLogic::getUserCallRecords($params);
        if (false === $result) {
            return $this->fail(VideoCallLogic::getError());
        }

        return $this->success('获取成功', $result);
    }
    
    /**
     * 获取通话统计
     */
    public function getCallStats()
    {
        // 验证参数
        $params = (new VideoCallValidate())->get()->goCheck('get_stats');

        // 添加用户ID
        $params['user_id'] = $this->userId;

        $result = VideoCallLogic::getCallStats($params);
        if (false === $result) {
            return $this->fail(VideoCallLogic::getError());
        }

        return $this->success('获取成功', $result);
    }
    
    /**
     * 通话质量上报
     */
    public function reportQuality()
    {
        // 验证参数
        $params = (new VideoCallValidate())->post()->goCheck('report_quality');

        // 添加用户ID
        $params['user_id'] = $this->userId;

        $result = VideoCallLogic::reportQuality($params);
        if (false === $result) {
            return $this->fail(VideoCallLogic::getError());
        }

        return $this->success('质量数据上报成功', [], 1, 1);
    }
    
    /**
     * 检查用户通话状态
     */
    public function checkCallStatus()
    {
        $result = VideoCallLogic::checkCallStatus($this->userId);
        if (false === $result) {
            return $this->fail(VideoCallLogic::getError());
        }

        return $this->success('获取成功', $result);
    }

    /**
     * 获取通话实时费用信息（前端轮询接口）
     */
    public function getCallCostInfo()
    {
        // 验证参数
        $params = (new VideoCallValidate())->post()->goCheck('get_cost_info');

        // 添加用户ID
        $params['user_id'] = $this->userId;

        $result = VideoCallLogic::getCallCostInfo($params);
        if (false === $result) {
            return $this->fail(VideoCallLogic::getError());
        }

        return $this->success('获取成功', $result);
    }

    /**
     * 心跳检测（通话中定期调用）
     */
    public function heartbeat()
    {
        // 验证参数
        $params = (new VideoCallValidate())->post()->goCheck('heartbeat');

        // 添加用户ID
        $params['user_id'] = $this->userId;

        $result = VideoCallLogic::heartbeat($params);
        if (false === $result) {
            return $this->fail(VideoCallLogic::getError());
        }

        return $this->success('心跳成功', $result);
    }
}
