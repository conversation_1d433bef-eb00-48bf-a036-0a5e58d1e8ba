<?php

namespace app\applent\controller\video;

use app\applent\controller\BaseApiController;
use app\applent\logic\video\VideoCallLogic;
use app\applent\validate\video\VideoCallValidate;
use think\facade\Cache;

/**
 * 视频通话控制器
 */
class VideoController extends BaseApiController
{
    /**
     * 发起通话
     */
    public function start_call()
    {
        $params = (new VideoCallValidate())->post()->goCheck('start_call');
        $params['user_id'] = $this->userId;
        $result = VideoCallLogic::startCall($params);
        if (false === $result) {
            return $this->fail(VideoCallLogic::getError());
        }
        return $this->success('通话发起成功', $result);
    }
}
