<?php

namespace app\common\service\agora;

use app\common\service\ConfigService;
use app\common\service\LogService;
use think\facade\Config;

/**
 * 声网(Agora)服务类
 */
class AgoraService
{
    private $appId;
    private $appCertificate;
    private $customerId;
    private $customerCertificate;
    
    public function __construct()
    {
        // 从配置中读取声网参数
        $this->appId = ConfigService::get('systemconfig', 'agora_app_id', '');
        $this->appCertificate = ConfigService::get('systemconfig', 'agora_app_certificate', '');
        
        // 验证必需配置
        if (empty($this->appId) || empty($this->appCertificate)) {
            throw new \Exception('声网配置不完整，请检查 agora.app_id、agora.app_certificate 配置');
        }
    }
    
    /**
     * 生成RTC Token
     * @param int $uid 用户ID
     * @param string $channelName 频道名称
     * @param string $role 用户角色 attendee|publisher|subscriber|admin
     * @param int $expireTime 过期时间(秒)，默认30天
     * @return string Token字符串
     */
    public function generateRtcToken($uid, $channelName, $role = 'attendee', $expireTime = null)
    {
        try {
            // 引入Token构建器
            require_once app_path() . 'applent/system/agora/src/RtcTokenBuilder.php';
            
            if ($expireTime === null) {
                $expireTime = Config::get('agora.token_expire_time', 86400 * 30);
            }
            
            // 角色映射
            $roleMap = [
                'attendee' => \RtcTokenBuilder::RoleAttendee,
                'publisher' => \RtcTokenBuilder::RolePublisher,
                'subscriber' => \RtcTokenBuilder::RoleSubscriber,
                'admin' => \RtcTokenBuilder::RoleAdmin,
            ];
            
            $agoraRole = $roleMap[$role] ?? \RtcTokenBuilder::RoleAttendee;
            
            $currentTimestamp = time();
            $privilegeExpiredTs = $currentTimestamp + $expireTime;
            
            $token = \RtcTokenBuilder::buildTokenWithUid(
                $this->appId,
                $this->appCertificate,
                $channelName,
                $uid,
                $agoraRole,
                $privilegeExpiredTs
            );
            
            LogService::write('生成RTC Token', [
                'uid' => $uid,
                'channel' => $channelName,
                'role' => $role,
                'expire_time' => $expireTime
            ], 'agora_token');
            
            return $token;
            
        } catch (\Exception $e) {
            LogService::write('生成RTC Token失败', [
                'uid' => $uid,
                'channel' => $channelName,
                'error' => $e->getMessage()
            ], 'agora_error');
            
            throw new \Exception('Token生成失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 生成RTM Token
     * @param string $userId 用户ID
     * @param int $expireTime 过期时间(秒)
     * @return string Token字符串
     */
    public function generateRtmToken($userId, $expireTime = null)
    {
        try {
            require_once app_path() . 'applent/system/agora/src/RtmTokenBuilder.php';
            
            if ($expireTime === null) {
                $expireTime = Config::get('agora.token_expire_time', 86400 * 30);
            }
            
            $currentTimestamp = time();
            $privilegeExpiredTs = $currentTimestamp + $expireTime;
            
            $token = \RtmTokenBuilder::buildToken(
                $this->appId,
                $this->appCertificate,
                $userId,
                \RtmTokenBuilder::RoleRtmUser,
                $privilegeExpiredTs
            );
            
            return $token;
            
        } catch (\Exception $e) {
            LogService::write('生成RTM Token失败', [
                'user_id' => $userId,
                'error' => $e->getMessage()
            ], 'agora_error');
            
            throw new \Exception('RTM Token生成失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 生成频道ID
     * @param int $userId1 用户1 ID
     * @param int $userId2 用户2 ID
     * @return string 频道ID
     */
    public function generateChannelId($userId1, $userId2 = null)
    {
        $timestamp = time();
        $random = mt_rand(1000, 9999);
        
        if ($userId2) {
            // 双人通话：确保频道ID唯一且可重现
            $userIds = [$userId1, $userId2];
            sort($userIds);
            return $timestamp . '_' . implode('_', $userIds) . '_' . $random;
        } else {
            // 单人或多人通话
            return $timestamp . '_' . $userId1 . '_' . $random;
        }
    }
    
    /**
     * 获取声网配置信息
     * @return array 配置信息
     */
    public function getConfig()
    {
        return [
            'app_id' => $this->appId,
            'recording_enabled' => Config::get('agora.recording.enabled', true),
            'quality_monitoring_enabled' => Config::get('agora.quality_monitoring.enabled', true),
            'max_users' => Config::get('agora.channel.max_users', 17),
            'auto_subscribe' => Config::get('agora.channel.auto_subscribe', true),
            'auto_publish' => Config::get('agora.channel.auto_publish', true),
        ];
    }
    
    /**
     * 验证Token是否有效
     * @param string $token Token字符串
     * @param string $channelName 频道名称
     * @param int $uid 用户ID
     * @return bool 是否有效
     */
    public function validateToken($token, $channelName, $uid)
    {
        try {
            // 这里可以实现Token验证逻辑
            // 由于声网SDK没有直接的验证方法，可以通过尝试解析Token来验证
            return !empty($token) && strlen($token) > 50;
        } catch (\Exception $e) {
            LogService::write('Token验证失败', [
                'channel' => $channelName,
                'uid' => $uid,
                'error' => $e->getMessage()
            ], 'agora_error');
            
            return false;
        }
    }
    
    /**
     * 获取录制配置
     * @return array 录制配置
     */
    public function getRecordingConfig()
    {
        return Config::get('agora.recording', []);
    }
    
    /**
     * 获取推流配置
     * @return array 推流配置
     */
    public function getStreamingConfig()
    {
        return Config::get('agora.streaming', []);
    }
    
    /**
     * 记录通话质量数据
     * @param string $channelId 频道ID
     * @param int $userId 用户ID
     * @param array $qualityData 质量数据
     * @return bool 是否成功
     */
    public function recordQualityData($channelId, $userId, $qualityData)
    {
        try {
            LogService::write('通话质量数据', [
                'channel_id' => $channelId,
                'user_id' => $userId,
                'quality_data' => $qualityData
            ], 'agora_quality');
            
            // 这里可以将质量数据存储到数据库
            // 例如：CallQualityModel::create($data);
            
            return true;
        } catch (\Exception $e) {
            LogService::write('记录通话质量数据失败', [
                'channel_id' => $channelId,
                'user_id' => $userId,
                'error' => $e->getMessage()
            ], 'agora_error');
            
            return false;
        }
    }
}
