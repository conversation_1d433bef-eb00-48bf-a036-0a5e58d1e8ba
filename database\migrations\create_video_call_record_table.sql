-- 视频通话记录表
CREATE TABLE `la_video_call_record` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '发起用户ID',
  `call_be_user_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '被叫用户ID',
  `channel_id` varchar(100) NOT NULL DEFAULT '' COMMENT '声网频道ID',
  `status` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '通话状态：0=发起通话,1=通话接通,2=通话结束,3=通话被拒绝,4=通话超时,5=通话取消',
  `type` tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '通话类型：1=视频通话,2=语音通话',
  `duration` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '通话时长(秒)',
  `charge_amount` decimal(10,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '扣费金额',
  `start_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '通话开始时间',
  `end_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '通话结束时间',
  `ip` varchar(45) NOT NULL DEFAULT '' COMMENT '发起者IP地址',
  `create_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
  `delete_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '删除时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_call_be_user_id` (`call_be_user_id`),
  KEY `idx_channel_id` (`channel_id`),
  KEY `idx_status` (`status`),
  KEY `idx_type` (`type`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_delete_time` (`delete_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='视频通话记录表';

-- 通话质量记录表
CREATE TABLE `la_video_call_quality` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `call_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '通话记录ID',
  `user_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '用户ID',
  `channel_id` varchar(100) NOT NULL DEFAULT '' COMMENT '频道ID',
  `network_quality` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '网络质量：1-6',
  `audio_quality` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '音频质量：1-6',
  `video_quality` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '视频质量：1-6',
  `audio_bitrate` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '音频码率',
  `video_bitrate` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '视频码率',
  `audio_packet_loss` decimal(5,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '音频丢包率(%)',
  `video_packet_loss` decimal(5,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '视频丢包率(%)',
  `rtt` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '往返时延(ms)',
  `cpu_usage` decimal(5,2) unsigned NOT NULL DEFAULT '0.00' COMMENT 'CPU使用率(%)',
  `memory_usage` decimal(5,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '内存使用率(%)',
  `report_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '上报时间',
  `create_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_call_id` (`call_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_channel_id` (`channel_id`),
  KEY `idx_report_time` (`report_time`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='通话质量记录表';

-- 通话计费记录表
CREATE TABLE `la_video_call_billing` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `call_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '通话记录ID',
  `user_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '付费用户ID',
  `receive_user_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '收费用户ID',
  `channel_id` varchar(100) NOT NULL DEFAULT '' COMMENT '频道ID',
  `billing_type` tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '计费类型：1=按分钟,2=按次',
  `unit_price` decimal(10,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '单价(钻石/分钟或钻石/次)',
  `billing_duration` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '计费时长(秒)',
  `total_amount` decimal(10,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '总费用(钻石)',
  `status` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '计费状态：0=待扣费,1=已扣费,2=扣费失败',
  `billing_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '计费时间',
  `create_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_call_id` (`call_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_receive_user_id` (`receive_user_id`),
  KEY `idx_channel_id` (`channel_id`),
  KEY `idx_status` (`status`),
  KEY `idx_billing_time` (`billing_time`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='通话计费记录表';

-- 添加用户表的视频通话价格字段（如果不存在）
ALTER TABLE `la_user` 
ADD COLUMN `video_price` decimal(10,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '视频通话价格(钻石/分钟)' AFTER `user_money`,
ADD COLUMN `voice_price` decimal(10,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '语音通话价格(钻石/分钟)' AFTER `video_price`;

-- 添加索引
ALTER TABLE `la_user` 
ADD INDEX `idx_video_price` (`video_price`),
ADD INDEX `idx_voice_price` (`voice_price`);
