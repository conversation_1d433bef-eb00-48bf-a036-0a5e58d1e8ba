<?php
/**
 * 声网集成测试脚本
 * 用于验证声网SDK集成是否正常
 */

require_once __DIR__ . '/vendor/autoload.php';

use app\common\service\agora\AgoraService;
use think\facade\Config;

// 初始化ThinkPHP
$app = new think\App();
$app->initialize();

echo "=== 声网(Agora)集成测试 ===\n\n";

try {
    // 1. 测试配置加载
    echo "1. 测试配置加载...\n";
    $appId = Config::get('agora.app_id');
    $appCertificate = Config::get('agora.app_certificate');
    
    if (empty($appId) || empty($appCertificate)) {
        echo "❌ 配置错误：请检查 config/agora.php 中的 app_id 和 app_certificate\n";
        exit(1);
    }
    
    echo "✅ 配置加载成功\n";
    echo "   App ID: " . substr($appId, 0, 8) . "...\n";
    echo "   Certificate: " . substr($appCertificate, 0, 8) . "...\n\n";
    
    // 2. 测试AgoraService实例化
    echo "2. 测试AgoraService实例化...\n";
    $agoraService = new AgoraService();
    echo "✅ AgoraService实例化成功\n\n";
    
    // 3. 测试Token生成
    echo "3. 测试Token生成...\n";
    $testUid = 12345;
    $testChannelName = 'test_channel_' . time();
    
    $token = $agoraService->generateRtcToken($testUid, $testChannelName, 'publisher');
    
    if (!empty($token) && strlen($token) > 50) {
        echo "✅ RTC Token生成成功\n";
        echo "   Channel: {$testChannelName}\n";
        echo "   UID: {$testUid}\n";
        echo "   Token: " . substr($token, 0, 20) . "...\n\n";
    } else {
        echo "❌ RTC Token生成失败\n";
        exit(1);
    }
    
    // 4. 测试RTM Token生成
    echo "4. 测试RTM Token生成...\n";
    $rtmToken = $agoraService->generateRtmToken((string)$testUid);
    
    if (!empty($rtmToken) && strlen($rtmToken) > 50) {
        echo "✅ RTM Token生成成功\n";
        echo "   Token: " . substr($rtmToken, 0, 20) . "...\n\n";
    } else {
        echo "❌ RTM Token生成失败\n";
        exit(1);
    }
    
    // 5. 测试频道ID生成
    echo "5. 测试频道ID生成...\n";
    $channelId1 = $agoraService->generateChannelId(100, 200);
    $channelId2 = $agoraService->generateChannelId(300);
    
    echo "✅ 频道ID生成成功\n";
    echo "   双人通话频道: {$channelId1}\n";
    echo "   单人频道: {$channelId2}\n\n";
    
    // 6. 测试配置获取
    echo "6. 测试配置获取...\n";
    $config = $agoraService->getConfig();
    
    if (is_array($config) && isset($config['app_id'])) {
        echo "✅ 配置获取成功\n";
        echo "   App ID: " . substr($config['app_id'], 0, 8) . "...\n";
        echo "   录制功能: " . ($config['recording_enabled'] ? '启用' : '禁用') . "\n";
        echo "   质量监控: " . ($config['quality_monitoring_enabled'] ? '启用' : '禁用') . "\n";
        echo "   最大用户数: {$config['max_users']}\n\n";
    } else {
        echo "❌ 配置获取失败\n";
        exit(1);
    }
    
    // 7. 测试Token验证
    echo "7. 测试Token验证...\n";
    $isValid = $agoraService->validateToken($token, $testChannelName, $testUid);
    
    if ($isValid) {
        echo "✅ Token验证成功\n\n";
    } else {
        echo "❌ Token验证失败\n";
        exit(1);
    }
    
    // 8. 测试质量数据记录
    echo "8. 测试质量数据记录...\n";
    $qualityData = [
        'network_quality' => 5,
        'audio_quality' => 4,
        'video_quality' => 5,
        'audio_bitrate' => 64000,
        'video_bitrate' => 500000,
        'audio_packet_loss' => 0.1,
        'video_packet_loss' => 0.2,
        'rtt' => 120,
        'cpu_usage' => 15.5,
        'memory_usage' => 25.8
    ];
    
    $result = $agoraService->recordQualityData($testChannelName, $testUid, $qualityData);
    
    if ($result) {
        echo "✅ 质量数据记录成功\n\n";
    } else {
        echo "❌ 质量数据记录失败\n";
        exit(1);
    }
    
    // 9. 测试录制配置
    echo "9. 测试录制配置...\n";
    $recordingConfig = $agoraService->getRecordingConfig();
    
    if (is_array($recordingConfig)) {
        echo "✅ 录制配置获取成功\n";
        echo "   录制功能: " . ($recordingConfig['enabled'] ? '启用' : '禁用') . "\n";
        echo "   存储厂商: {$recordingConfig['storage_vendor']}\n";
        echo "   文件格式: " . implode(', ', $recordingConfig['file_types']) . "\n\n";
    } else {
        echo "❌ 录制配置获取失败\n";
        exit(1);
    }
    
    // 10. 测试推流配置
    echo "10. 测试推流配置...\n";
    $streamingConfig = $agoraService->getStreamingConfig();
    
    if (is_array($streamingConfig)) {
        echo "✅ 推流配置获取成功\n";
        echo "   推流功能: " . ($streamingConfig['enabled'] ? '启用' : '禁用') . "\n";
        echo "   画布尺寸: {$streamingConfig['canvas_width']}x{$streamingConfig['canvas_height']}\n";
        echo "   码率: {$streamingConfig['bitrate']} kbps\n";
        echo "   帧率: {$streamingConfig['fps']} fps\n\n";
    } else {
        echo "❌ 推流配置获取失败\n";
        exit(1);
    }
    
    echo "🎉 所有测试通过！声网集成正常工作。\n\n";
    
    echo "=== 测试总结 ===\n";
    echo "✅ 配置加载正常\n";
    echo "✅ 服务实例化正常\n";
    echo "✅ RTC Token生成正常\n";
    echo "✅ RTM Token生成正常\n";
    echo "✅ 频道ID生成正常\n";
    echo "✅ 配置获取正常\n";
    echo "✅ Token验证正常\n";
    echo "✅ 质量数据记录正常\n";
    echo "✅ 录制配置正常\n";
    echo "✅ 推流配置正常\n\n";
    
    echo "现在可以开始使用声网功能了！\n";
    echo "API接口地址: /api/video/\n";
    echo "文档位置: Docs/声网集成指南.md\n";
    
} catch (\Exception $e) {
    echo "❌ 测试失败: " . $e->getMessage() . "\n";
    echo "错误文件: " . $e->getFile() . ":" . $e->getLine() . "\n";
    echo "\n请检查以下配置：\n";
    echo "1. config/agora.php 配置文件是否正确\n";
    echo "2. .env 文件中的声网参数是否设置\n";
    echo "3. app/applent/system/agora/src/ 目录下的SDK文件是否存在\n";
    echo "4. 数据库连接是否正常\n";
    exit(1);
}
?>
