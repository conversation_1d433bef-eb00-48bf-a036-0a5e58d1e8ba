# 前端音视频集成指南

## Web端集成 (AgoraRTC Web SDK)

### 1. SDK引入

```html
<!-- 引入声网Web SDK -->
<script src="/static/js/AgoraRTC_N-4.18.3.js"></script>
<script src="/static/js/index.js"></script>
```

### 2. 基础配置

```javascript
// 创建Agora客户端
var client = AgoraRTC.createClient({ mode: "rtc", codec: "vp8" });

// 本地音视频轨道
var localTracks = {
  videoTrack: null,
  audioTrack: null
};

// 远端用户集合
var remoteUsers = {};

// 客户端配置选项
var options = {
  appid: null,      // 声网应用ID
  channel: null,    // 频道名称
  uid: null,        // 用户ID
  token: null       // 访问Token
};
```

### 3. 核心功能实现

#### 3.1 加入频道

```javascript
async function join() {
  // 添加事件监听
  client.on("user-published", handleUserPublished);
  client.on("user-unpublished", handleUserUnpublished);
  
  // 获取URL参数
  var urlParams = new URL(location.href).searchParams;
  options.appid = urlParams.get("appid");
  options.channel = urlParams.get("channel");
  options.token = urlParams.get("token");
  options.uid = urlParams.get("uid");
  
  // 加入频道
  [options.uid, localTracks.audioTrack, localTracks.videoTrack] = await Promise.all([
    client.join(options.appid, options.channel, options.token || null, options.uid || null),
    AgoraRTC.createMicrophoneAudioTrack(),
    AgoraRTC.createCameraVideoTrack()
  ]);
  
  // 播放本地视频
  localTracks.videoTrack.play("local-player");
  
  // 发布本地音视频
  await client.publish(Object.values(localTracks));
  console.log("发布成功");
}
```

#### 3.2 处理远端用户

```javascript
// 远端用户发布音视频
function handleUserPublished(user, mediaType) {
  const id = user.uid;
  remoteUsers[id] = user;
  
  subscribe(user, mediaType);
}

// 远端用户取消发布
function handleUserUnpublished(user, mediaType) {
  if (mediaType === 'video') {
    const id = user.uid;
    delete remoteUsers[id];
    $(`#player-wrapper-${id}`).remove();
  }
}

// 订阅远端用户
async function subscribe(user, mediaType) {
  const uid = user.uid;
  
  // 订阅指定用户
  await client.subscribe(user, mediaType);
  console.log("订阅成功");
  
  if (mediaType === 'video') {
    const player = $(`
      <div id="player-wrapper-${uid}">
        <p class="player-name">远端用户 ${uid}</p>
        <div id="player-${uid}" class="player"></div>
      </div>
    `);
    $("#remote-playerlist").append(player);
    user.videoTrack.play(`player-${uid}`);
  }
  
  if (mediaType === 'audio') {
    user.audioTrack.play();
  }
}
```

#### 3.3 离开频道

```javascript
async function leave() {
  for (trackName in localTracks) {
    var track = localTracks[trackName];
    if(track) {
      track.stop();
      track.close();
      localTracks[trackName] = undefined;
    }
  }
  
  // 移除远端用户
  remoteUsers = {};
  $("#remote-playerlist").html("");
  
  // 离开频道
  await client.leave();
  
  $("#local-player").html("");
  $("#join").attr("disabled", false);
  $("#leave").attr("disabled", true);
  console.log("客户端离开频道成功");
}
```

### 4. 设备管理

#### 4.1 摄像头切换

```javascript
async function switchCamera() {
  const videoTrack = localTracks.videoTrack;
  const cameras = await AgoraRTC.getCameras();
  
  // 切换到下一个摄像头
  const currentCamera = videoTrack.getMediaStreamTrack().getSettings().deviceId;
  const currentIndex = cameras.findIndex(camera => camera.deviceId === currentCamera);
  const nextIndex = (currentIndex + 1) % cameras.length;
  
  await videoTrack.setDevice(cameras[nextIndex].deviceId);
}
```

#### 4.2 麦克风切换

```javascript
async function switchMicrophone() {
  const audioTrack = localTracks.audioTrack;
  const microphones = await AgoraRTC.getMicrophones();
  
  // 切换到下一个麦克风
  const currentMic = audioTrack.getMediaStreamTrack().getSettings().deviceId;
  const currentIndex = microphones.findIndex(mic => mic.deviceId === currentMic);
  const nextIndex = (currentIndex + 1) % microphones.length;
  
  await audioTrack.setDevice(microphones[nextIndex].deviceId);
}
```

### 5. 音视频控制

#### 5.1 静音/取消静音

```javascript
async function muteAudio() {
  if (!localTracks.audioTrack) return;
  
  await localTracks.audioTrack.setMuted(true);
  console.log("麦克风已静音");
}

async function unmuteAudio() {
  if (!localTracks.audioTrack) return;
  
  await localTracks.audioTrack.setMuted(false);
  console.log("麦克风已取消静音");
}
```

#### 5.2 开启/关闭摄像头

```javascript
async function muteVideo() {
  if (!localTracks.videoTrack) return;
  
  await localTracks.videoTrack.setMuted(true);
  console.log("摄像头已关闭");
}

async function unmuteVideo() {
  if (!localTracks.videoTrack) return;
  
  await localTracks.videoTrack.setMuted(false);
  console.log("摄像头已开启");
}
```

## 移动端集成

### 1. iOS集成

#### 1.1 SDK导入

```swift
// Podfile
pod 'AgoraRtcEngine_iOS'
pod 'AgoraRtm_iOS'
```

#### 1.2 基础配置

```swift
import AgoraRtcKit

class VideoCallViewController: UIViewController {
    var agoraKit: AgoraRtcEngineKit!
    
    override func viewDidLoad() {
        super.viewDidLoad()
        initializeAgoraEngine()
    }
    
    func initializeAgoraEngine() {
        agoraKit = AgoraRtcEngineKit.sharedEngine(withAppId: AppID, delegate: self)
        agoraKit.enableVideo()
        agoraKit.setVideoEncoderConfiguration(
            AgoraVideoEncoderConfiguration(
                size: AgoraVideoDimension640x360,
                frameRate: .fps15,
                bitrate: AgoraVideoBitrateStandard,
                orientationMode: .adaptative
            )
        )
    }
}
```

### 2. Android集成

#### 2.1 SDK导入

```gradle
// app/build.gradle
implementation 'io.agora.rtc:full-sdk:4.1.1'
implementation 'io.agora.rtm:rtm-sdk:1.5.1'
```

#### 2.2 基础配置

```java
import io.agora.rtc2.RtcEngine;
import io.agora.rtc2.RtcEngineConfig;

public class VideoCallActivity extends AppCompatActivity {
    private RtcEngine mRtcEngine;
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_video_call);
        
        initializeAgoraEngine();
    }
    
    private void initializeAgoraEngine() {
        try {
            RtcEngineConfig config = new RtcEngineConfig();
            config.mContext = getBaseContext();
            config.mAppId = getString(R.string.agora_app_id);
            config.mEventHandler = mRtcEventHandler;
            
            mRtcEngine = RtcEngine.create(config);
            mRtcEngine.enableVideo();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
```

## 监控管理后台

### 1. 页面结构

```html
<!-- 通话监控页面 -->
<!DOCTYPE html>
<html>
<head>
    <title>通话监控</title>
    <script src="/static/js/AgoraRTC_N-4.18.3.js"></script>
</head>
<body>
    <div class="container">
        <div class="alert alert-danger">
            <strong>提示!</strong> 监控大概延迟15-20s
        </div>
        
        <div class="row">
            <div class="col-md-6">
                <h4>用户A</h4>
                <div id="player-a" class="video-player"></div>
            </div>
            <div class="col-md-6">
                <h4>用户B</h4>
                <div id="player-b" class="video-player"></div>
            </div>
        </div>
        
        <div class="controls">
            <button onclick="startMonitoring()">开始监控</button>
            <button onclick="stopMonitoring()">停止监控</button>
        </div>
    </div>
</body>
</html>
```

### 2. 监控脚本

```javascript
let monitorClient = null;

async function startMonitoring() {
    const channelId = getChannelIdFromUrl();
    const appId = getAppIdFromConfig();
    const token = await getMonitorToken();
    
    monitorClient = AgoraRTC.createClient({ mode: "rtc", codec: "vp8" });
    
    // 监听远端用户
    monitorClient.on("user-published", async (user, mediaType) => {
        await monitorClient.subscribe(user, mediaType);
        
        if (mediaType === 'video') {
            const playerId = `player-${user.uid}`;
            user.videoTrack.play(playerId);
        }
    });
    
    // 加入频道进行监控
    await monitorClient.join(appId, channelId, token, 99999);
    console.log("监控已开始");
}

async function stopMonitoring() {
    if (monitorClient) {
        await monitorClient.leave();
        monitorClient = null;
        console.log("监控已停止");
    }
}
```

## 常见问题解决

### 1. 权限问题

```javascript
// 检查浏览器权限
async function checkPermissions() {
    try {
        const stream = await navigator.mediaDevices.getUserMedia({
            video: true,
            audio: true
        });
        stream.getTracks().forEach(track => track.stop());
        return true;
    } catch (error) {
        console.error("权限获取失败:", error);
        return false;
    }
}
```

### 2. 网络检测

```javascript
// 网络质量检测
client.on("network-quality", (stats) => {
    console.log("网络质量:", stats.uplinkNetworkQuality, stats.downlinkNetworkQuality);
    
    // 根据网络质量调整视频参数
    if (stats.uplinkNetworkQuality >= 4) {
        // 网络较差，降低视频质量
        localTracks.videoTrack.setEncoderConfiguration({
            width: 320,
            height: 240,
            frameRate: 10,
            bitrateMin: 200,
            bitrateMax: 400
        });
    }
});
```

### 3. 错误处理

```javascript
// 全局错误处理
client.on("exception", (evt) => {
    console.error("SDK异常:", evt);
    
    // 根据错误类型进行处理
    switch(evt.code) {
        case "INVALID_PARAMS":
            alert("参数错误，请检查配置");
            break;
        case "NETWORK_ERROR":
            alert("网络连接异常，请检查网络");
            break;
        default:
            alert("未知错误: " + evt.msg);
    }
});
```

---

**注意**: 
1. 确保在HTTPS环境下使用，HTTP环境无法获取摄像头和麦克风权限
2. 移动端需要适配不同屏幕尺寸和设备性能
3. 监控功能需要管理员权限，注意权限控制
