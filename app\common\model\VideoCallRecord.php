<?php

namespace app\common\model;

use think\Model;
use app\common\model\user\User;
/**
 * 视频通话记录模型
 */
class VideoCallRecord extends Model
{
    // 状态常量
    const STATUS_INITIATED = 0;    // 发起通话
    const STATUS_CONNECTED = 1;    // 通话接通
    const STATUS_ENDED = 2;        // 通话结束
    const STATUS_REJECTED = 3;     // 通话被拒绝
    const STATUS_TIMEOUT = 4;      // 通话超时
    const STATUS_CANCELLED = 5;    // 通话取消
    const STATUS_ABNORMAL = 6;     // 异常结束（如用户杀死后台）

    // 结束原因常量
    const END_REASON_NORMAL = 'normal';           // 正常挂断
    const END_REASON_TIMEOUT = 'timeout';         // 超时
    const END_REASON_REJECTED = 'rejected';       // 被拒绝
    const END_REASON_CANCELLED = 'cancelled';     // 取消
    const END_REASON_NETWORK = 'network';         // 网络异常
    const END_REASON_BALANCE = 'balance';         // 余额不足
    const END_REASON_ABNORMAL = 'abnormal';       // 异常断开
    
    // 通话类型常量
    const TYPE_VIDEO = 1;          // 视频通话
    const TYPE_VOICE = 2;          // 语音通话

    // 挂断类型常量
    const HANGUP_TYPE_TIMEOUT = 1;      // 超时挂断
    const HANGUP_TYPE_CALLER = 2;       // 发起人挂断
    const HANGUP_TYPE_CALLEE = 3;       // 接听人挂断
    const HANGUP_TYPE_SYSTEM = 4;       // 系统挂断

    protected $table = 'la_video_call_record';

    // 自动时间戳
    protected $autoWriteTimestamp = true;
    protected $createTime = 'create_time';
    protected $updateTime = 'update_time';
    /**
     * 获取正在进行的通话
     */
    public static function getActiveCall($userId)
    {
        return self::where(function($query) use ($userId) {
                $query->where('user_id', $userId)
                      ->whereOr('call_be_user_id', $userId);
            })
            ->whereIn('status', [self::STATUS_INITIATED, self::STATUS_CONNECTED])
            ->order('create_time', 'desc')
            ->find();
    }
    
    /**
     * 检查用户是否在通话中
     */
    public static function isUserInCall($userId)
    {
        $activeCall = self::getActiveCall($userId);
        return !empty($activeCall);
    }
}
