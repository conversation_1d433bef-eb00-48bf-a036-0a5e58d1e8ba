<?php

namespace app\common\model;

use think\Model;
use app\common\model\user\User;
/**
 * 视频通话记录模型
 */
class VideoCallRecord extends Model
{
    protected $name = 'video_call_record';
    
    // 设置字段信息
    protected $schema = [
        'id'                => 'int',
        'user_id'           => 'int',
        'call_be_user_id'   => 'int',
        'channel_id'        => 'string',
        'status'            => 'int',
        'type'              => 'int',
        'duration'          => 'int',
        'total_cost'        => 'decimal',      // 总费用
        'callee_earnings'   => 'decimal',      // 被叫方收益
        'platform_earnings' => 'decimal',      // 平台收益
        'price_per_minute'  => 'decimal',      // 每分钟价格
        'connect_time'      => 'datetime',     // 接通时间
        'end_time'          => 'datetime',     // 结束时间
        'last_heartbeat'    => 'datetime',     // 最后心跳时间
        'caller_online'     => 'int',          // 主叫方在线状态 1=在线 0=离线
        'callee_online'     => 'int',          // 被叫方在线状态 1=在线 0=离线
        'end_reason'        => 'string',       // 结束原因
        'is_settled'        => 'int',          // 是否已结算 1=已结算 0=未结算
        'settle_time'       => 'datetime',     // 结算时间
        'extra_data'        => 'text',         // 额外数据(JSON)
        'create_time'       => 'int',
        'update_time'       => 'int',
        'delete_time'       => 'int',
    ];
    
    // 自动时间戳
    protected $autoWriteTimestamp = true;
    protected $createTime = 'create_time';
    protected $updateTime = 'update_time';
    protected $deleteTime = 'delete_time';
    
    // 软删除
    use \think\model\concern\SoftDelete;
    
    // 状态常量
    const STATUS_INITIATED = 0;    // 发起通话
    const STATUS_CONNECTED = 1;    // 通话接通
    const STATUS_ENDED = 2;        // 通话结束
    const STATUS_REJECTED = 3;     // 通话被拒绝
    const STATUS_TIMEOUT = 4;      // 通话超时
    const STATUS_CANCELLED = 5;    // 通话取消
    const STATUS_ABNORMAL = 6;     // 异常结束（如用户杀死后台）

    // 结束原因常量
    const END_REASON_NORMAL = 'normal';           // 正常挂断
    const END_REASON_TIMEOUT = 'timeout';         // 超时
    const END_REASON_REJECTED = 'rejected';       // 被拒绝
    const END_REASON_CANCELLED = 'cancelled';     // 取消
    const END_REASON_NETWORK = 'network';         // 网络异常
    const END_REASON_BALANCE = 'balance';         // 余额不足
    const END_REASON_ABNORMAL = 'abnormal';       // 异常断开
    
    // 通话类型常量
    const TYPE_VIDEO = 1;          // 视频通话
    const TYPE_VOICE = 2;          // 语音通话
    
    /**
     * 状态文本映射
     */
    public static function getStatusText($status)
    {
        $statusMap = [
            self::STATUS_INITIATED => '发起通话',
            self::STATUS_CONNECTED => '通话接通',
            self::STATUS_ENDED => '通话结束',
            self::STATUS_REJECTED => '通话被拒绝',
            self::STATUS_TIMEOUT => '通话超时',
            self::STATUS_CANCELLED => '通话取消',
        ];
        
        return $statusMap[$status] ?? '未知状态';
    }
    
    /**
     * 通话类型文本映射
     */
    public static function getTypeText($type)
    {
        $typeMap = [
            self::TYPE_VIDEO => '视频通话',
            self::TYPE_VOICE => '语音通话',
        ];
        
        return $typeMap[$type] ?? '未知类型';
    }
    
    /**
     * 关联发起用户
     */
    public function caller()
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }
    
    /**
     * 关联被叫用户
     */
    public function callee()
    {
        return $this->belongsTo(User::class, 'call_be_user_id', 'id');
    }
    
    /**
     * 获取通话时长（格式化）
     */
    public function getDurationTextAttr($value, $data)
    {
        $duration = $data['duration'] ?? 0;
        
        if ($duration < 60) {
            return $duration . '秒';
        } elseif ($duration < 3600) {
            $minutes = floor($duration / 60);
            $seconds = $duration % 60;
            return $minutes . '分' . ($seconds > 0 ? $seconds . '秒' : '');
        } else {
            $hours = floor($duration / 3600);
            $minutes = floor(($duration % 3600) / 60);
            $seconds = $duration % 60;
            return $hours . '时' . ($minutes > 0 ? $minutes . '分' : '') . ($seconds > 0 ? $seconds . '秒' : '');
        }
    }
    
    /**
     * 获取状态文本
     */
    public function getStatusTextAttr($value, $data)
    {
        return self::getStatusText($data['status'] ?? 0);
    }
    
    /**
     * 获取类型文本
     */
    public function getTypeTextAttr($value, $data)
    {
        return self::getTypeText($data['type'] ?? 1);
    }
    
    /**
     * 创建通话记录
     */
    public static function createCall($callerId, $calleeId, $channelId, $type = self::TYPE_VIDEO)
    {
        return self::create([
            'user_id' => $callerId,
            'call_be_user_id' => $calleeId,
            'channel_id' => $channelId,
            'type' => $type,
            'status' => self::STATUS_INITIATED,
            'start_time' => time(),
        ]);
    }
    
    /**
     * 更新通话状态
     */
    public function updateStatus($status, $additionalData = [])
    {
        $updateData = array_merge(['status' => $status], $additionalData);
        
        // 如果是结束通话，记录结束时间和计算时长
        if ($status == self::STATUS_ENDED && $this->start_time) {
            $updateData['end_time'] = time();
            $updateData['duration'] = $updateData['end_time'] - $this->start_time;
        }
        
        return $this->save($updateData);
    }
    
    /**
     * 获取用户通话记录
     */
    public static function getUserCallRecords($userId, $page = 1, $limit = 20)
    {
        return self::where(function($query) use ($userId) {
                $query->where('user_id', $userId)
                      ->whereOr('call_be_user_id', $userId);
            })
            ->with(['caller', 'callee'])
            ->order('create_time', 'desc')
            ->page($page, $limit)
            ->select();
    }
    
    /**
     * 获取正在进行的通话
     */
    public static function getActiveCall($userId)
    {
        return self::where(function($query) use ($userId) {
                $query->where('user_id', $userId)
                      ->whereOr('call_be_user_id', $userId);
            })
            ->whereIn('status', [self::STATUS_INITIATED, self::STATUS_CONNECTED])
            ->order('create_time', 'desc')
            ->find();
    }
    
    /**
     * 检查用户是否在通话中
     */
    public static function isUserInCall($userId)
    {
        $activeCall = self::getActiveCall($userId);
        return !empty($activeCall);
    }
    
    /**
     * 根据频道ID获取通话记录
     */
    public static function getCallByChannel($channelId)
    {
        return self::where('channel_id', $channelId)
                   ->order('create_time', 'desc')
                   ->find();
    }
    
    /**
     * 获取通话统计数据
     */
    public static function getCallStats($userId, $startTime = null, $endTime = null)
    {
        $query = self::where(function($q) use ($userId) {
            $q->where('user_id', $userId)
              ->whereOr('call_be_user_id', $userId);
        });
        
        if ($startTime) {
            $query->where('create_time', '>=', $startTime);
        }
        
        if ($endTime) {
            $query->where('create_time', '<=', $endTime);
        }
        
        $totalCalls = $query->count();
        $totalDuration = $query->sum('duration');
        $videoCallCount = $query->where('type', self::TYPE_VIDEO)->count();
        $voiceCallCount = $query->where('type', self::TYPE_VOICE)->count();
        
        return [
            'total_calls' => $totalCalls,
            'total_duration' => $totalDuration,
            'video_call_count' => $videoCallCount,
            'voice_call_count' => $voiceCallCount,
            'average_duration' => $totalCalls > 0 ? round($totalDuration / $totalCalls, 2) : 0,
        ];
    }

    /**
     * 更新心跳时间
     */
    public function updateHeartbeat($userId)
    {
        $field = ($this->user_id == $userId) ? 'caller_online' : 'callee_online';

        $this->save([
            'last_heartbeat' => date('Y-m-d H:i:s'),
            $field => 1
        ]);
    }

    /**
     * 标记用户离线
     */
    public function markUserOffline($userId)
    {
        $field = ($this->user_id == $userId) ? 'caller_online' : 'callee_online';

        $this->save([
            $field => 0
        ]);
    }

    /**
     * 检查是否有异常通话（超过一定时间没有心跳）
     */
    public static function checkAbnormalCalls()
    {
        $timeout = 60; // 60秒没有心跳认为异常
        $timeoutTime = date('Y-m-d H:i:s', time() - $timeout);

        return self::where('status', self::STATUS_CONNECTED)
            ->where('last_heartbeat', '<', $timeoutTime)
            ->select();
    }

    /**
     * 处理异常通话
     */
    public function handleAbnormalEnd($reason = self::END_REASON_ABNORMAL)
    {
        // 更新状态为异常结束
        $this->status = self::STATUS_ABNORMAL;
        $this->end_reason = $reason;
        $this->end_time = date('Y-m-d H:i:s');

        // 如果还没有结算，进行结算
        if (!$this->is_settled) {
            $this->processAbnormalSettlement();
        }

        $this->save();
    }

    /**
     * 处理异常结算
     */
    private function processAbnormalSettlement()
    {
        if (!$this->connect_time) {
            // 如果没有接通，不需要结算
            $this->is_settled = 1;
            $this->settle_time = date('Y-m-d H:i:s');
            return;
        }

        // 计算到最后心跳时间的通话时长
        $endTime = $this->last_heartbeat ?: $this->connect_time;
        $startTime = $this->connect_time;

        $duration = strtotime($endTime) - strtotime($startTime);

        if ($duration > 0) {
            $this->duration = $duration;

            // 调用Logic层的结算方法
            $settlementResult = \app\applent\logic\video\VideoCallLogic::processAbnormalSettlement($this);

            if ($settlementResult) {
                $this->total_cost = $settlementResult['total_cost'] ?? 0;
                $this->callee_earnings = $settlementResult['callee_earnings'] ?? 0;
                $this->platform_earnings = $settlementResult['platform_earnings'] ?? 0;
            }
        }

        $this->is_settled = 1;
        $this->settle_time = date('Y-m-d H:i:s');
    }

    /**
     * 获取需要定时检查的通话记录
     */
    public static function getCallsNeedCheck()
    {
        return self::where('status', 'in', [self::STATUS_INITIATED, self::STATUS_CONNECTED])
            ->where('is_settled', 0)
            ->select();
    }
}
