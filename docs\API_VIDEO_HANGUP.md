# 视频通话挂断API文档

## 接口概述

本接口用于处理视频通话的挂断操作，支持多种挂断场景：超时、发起人挂断、接听人挂断、系统挂断等。

## 接口信息

- **接口地址**: `/applent/video/hangup_call`
- **请求方式**: `POST`
- **接口说明**: 挂断视频通话
- **需要登录**: 是

## 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| call_id | int | 是 | 通话记录ID |
| hangup_type | int | 是 | 挂断类型：1=超时，2=发起人挂断，3=接听人挂断，4=系统挂断 |

### 挂断类型说明

1. **超时挂断 (hangup_type=1)**
   - 通话发起后，在规定时间内未接听
   - 状态变更为：`STATUS_TIMEOUT`

2. **发起人挂断 (hangup_type=2)**
   - 发起通话的用户主动挂断
   - 未接通时状态变更为：`STATUS_CANCELLED`
   - 已接通时状态变更为：`STATUS_ENDED`

3. **接听人挂断 (hangup_type=3)**
   - 接听通话的用户主动挂断
   - 未接通时状态变更为：`STATUS_REJECTED`
   - 已接通时状态变更为：`STATUS_ENDED`

4. **系统挂断 (hangup_type=4)**
   - 系统异常或其他原因导致的挂断
   - 状态变更为：`STATUS_ABNORMAL`

## 请求示例

```json
{
    "call_id": 12345,
    "hangup_type": 2
}
```

## 响应参数

### 成功响应

```json
{
    "code": 1,
    "msg": "通话挂断成功",
    "data": {
        "call_id": 12345,
        "status": 5,
        "duration": 120,
        "end_time": 1703123456,
        "hangup_type": 2
    }
}
```

### 响应字段说明

| 字段名 | 类型 | 说明 |
|--------|------|------|
| call_id | int | 通话记录ID |
| status | int | 更新后的通话状态 |
| duration | int | 通话时长（秒），未接通为0 |
| end_time | int | 通话结束时间戳 |
| hangup_type | int | 挂断类型 |

### 错误响应

```json
{
    "code": 0,
    "msg": "错误信息",
    "data": []
}
```

## 状态码说明

| 状态码 | 说明 |
|--------|------|
| 0 | 发起通话 |
| 1 | 通话接通 |
| 2 | 通话结束 |
| 3 | 通话被拒绝 |
| 4 | 通话超时 |
| 5 | 通话取消 |
| 6 | 异常结束 |

## 业务逻辑

1. **权限验证**：只有通话参与者（发起人或接听人）才能挂断通话
2. **状态检查**：只有进行中的通话（发起中或已接通）才能被挂断
3. **时长计算**：只有已接通的通话才会计算通话时长
4. **IM通知**：挂断后会自动发送IM消息通知对方
5. **缓存清理**：自动清理相关的通话缓存

## 错误码

| 错误码 | 错误信息 | 说明 |
|--------|----------|------|
| 0 | 通话记录不存在 | 指定的call_id不存在 |
| 0 | 该通话已结束，无法挂断 | 通话已经结束 |
| 0 | 您没有权限挂断此通话 | 非通话参与者 |
| 0 | 只有发起人才能执行发起人挂断操作 | 权限不匹配 |
| 0 | 只有接听人才能执行接听人挂断操作 | 权限不匹配 |
| 0 | 无效的挂断类型 | hangup_type参数错误 |
| 0 | 更新通话记录失败 | 数据库操作失败 |

## 使用场景

### 1. 超时挂断
```javascript
// 前端定时器检测超时
setTimeout(() => {
    if (!callConnected) {
        hangupCall(callId, 1); // 超时挂断
    }
}, 30000); // 30秒超时
```

### 2. 用户主动挂断
```javascript
// 发起人点击挂断按钮
function onCallerHangup() {
    hangupCall(callId, 2); // 发起人挂断
}

// 接听人点击挂断按钮
function onCalleeHangup() {
    hangupCall(callId, 3); // 接听人挂断
}
```

### 3. 系统异常挂断
```javascript
// 网络异常或其他系统错误
function onSystemError() {
    hangupCall(callId, 4); // 系统挂断
}
```

## 注意事项

1. 挂断操作是不可逆的，请确认后再调用
2. 挂断后会自动发送IM通知，无需额外处理
3. 通话时长只在已接通状态下才会计算
4. 系统会自动清理相关缓存和资源
5. 建议在前端添加防重复提交机制
