<?php

namespace app\applent\logic\video;

use app\common\logic\BaseLogic;
use app\common\model\user\User;
use app\common\model\VideoCallRecord;
use app\common\service\agora\AgoraService;
use app\common\service\LogService;
use app\common\service\ConfigService;
use app\common\enum\ImMessageEnum;
use think\facade\Cache;
use think\facade\Db;
use app\common\model\user\UserBalance;
/**
 * 视频通话业务逻辑类
 */
class VideoCallLogic extends BaseLogic
{
    /**
     * 发起通话
     * @param array $params 通话参数
     * @return array|false
     */
    public static function startCall($params)
    {
        Db::startTrans();
        try {
            if($params['to_user_id'] == $params['user_id']){
                throw new \Exception('不能给自己发起通话');
            }

            // 检查用户状态
            if (VideoCallRecord::isUserInCall($params['user_id'])) {
                throw new \Exception('您正在通话中，无法发起新的通话');
            }

            if (VideoCallRecord::isUserInCall($params['to_user_id'])) {
                throw new \Exception('对方正在通话中，请稍后再试');
            }

            //查找接听人信息
            $toUserInfo = User::where('id', $params['to_user_id'])
                        ->field('id,nickname,avatar,sex,video_price,voice_price,is_service_staff,is_auth,is_disable')
                        ->find();
            if (!$toUserInfo) {
                throw new \Exception('您所拨打的用户不存在');
            }
            if($toUserInfo['is_disable']){
                throw new \Exception('该用户已禁用');
            }
            if(!$toUserInfo['is_service_staff'] && $toUserInfo['sex'] == 2 && $toUserInfo['is_auth'] != 1){
                throw new \Exception('对方未认证，无法发起通话');
            }
            //检查接听人每分钟价格
            $price = $params['call_type'] == 1 ? $toUserInfo['video_price'] : $toUserInfo['voice_price'];
            if($toUserInfo['is_service_staff']){
                $price = 0;
            }

            //查找发起人余额
            $userBalance = UserBalance::getUserBalance($params['user_id']);
            $beforeSenderBalance = $userBalance ? $userBalance['balance'] : 0;
            if ($price > $beforeSenderBalance) {
                throw new \Exception('余额不足，请先充值');
            }
            
            //生成频道ID和Token
            $agoraService = new AgoraService();
            $channelId = $agoraService->generateChannelId($params['user_id'], $params['to_user_id']);      //通道ID
            $callerToken = $agoraService->generateRtcToken($params['user_id'], $channelId, 'publisher');   //发起人Token
            $calleeToken = $agoraService->generateRtcToken($params['to_user_id'], $channelId, 'publisher');//接听人Token

            // 创建通话记录
            $callRecord = [
                'user_id'           => $params['user_id'],
                'call_be_user_id'   => $params['to_user_id'],
                'channel_id'        => $channelId,
                'status'            => 0,
                'type'              => $params['call_type'],  
                'duration'          => 0,
                'unit_price'        => $price,
                'hangup_user_id'    => 0,
                'ip'                => request()->ip(),
                'create_time'       =>time(),
                'end_time'          => 0,
                
            ];
            $callRecordId = VideoCallRecord::insertGetId($callRecord);
            if (!$callRecordId) {
                return self::setError('创建通话记录失败');
            }

            Db::commit();

            return [
                'call_id'           => $callRecordId,
                'channel_id'        => $channelId,
                'user_token'        => $callerToken,
                'to_user_token'     => $calleeToken,
                'video_deduction'   => $price,
                'to_user_info'      => [
                    'id'            => $toUserInfo->id,
                    'nickname'      => $toUserInfo->nickname,
                    'avatar'        => $toUserInfo->avatar,
                    'video_price'   => $toUserInfo->video_price,
                    'voice_price'   => $toUserInfo->voice_price,
                ],
            ];

        } catch (\Exception $e) {
            Db::rollback();
            LogService::write('发起通话异常', [
                'caller_id' => $params['user_id'] ?? 0,
                'callee_id' => $params['to_user_id'] ?? 0,
                'error' => $e->getMessage()
            ], 'video_call_error');

            self::setError($e->getMessage());
            return false;
        }
    }

    /**
     * 挂断通话
     * @param array $params 挂断参数
     * @return array|false
     */
    public static function hangupCall($params)
    {
        Db::startTrans();
        try {
            // 查找通话记录
            $callRecord = VideoCallRecord::where('id', $params['call_id'])->find();
            if (!$callRecord) {
                throw new \Exception('通话记录不存在');
            }

            // 检查通话状态，只有发起中或已接通的通话才能挂断
            if (!in_array($callRecord['status'], [VideoCallRecord::STATUS_INITIATED, VideoCallRecord::STATUS_CONNECTED])) {
                throw new \Exception('该通话已结束，无法挂断');
            }

            // 检查用户权限（只有通话参与者才能挂断）
            if (!in_array($params['user_id'], [$callRecord['user_id'], $callRecord['call_be_user_id']])) {
                throw new \Exception('您没有权限挂断此通话');
            }

            // 根据挂断类型设置状态和结束原因
            $status = VideoCallRecord::STATUS_ENDED;
            $endReason = VideoCallRecord::END_REASON_NORMAL;

            switch ($params['hangup_type']) {
                case 1: // 超时
                    $status = VideoCallRecord::STATUS_TIMEOUT;
                    $endReason = VideoCallRecord::END_REASON_TIMEOUT;
                    break;
                case 2: // 发起人挂断
                    if ($params['user_id'] != $callRecord['user_id']) {
                        throw new \Exception('只有发起人才能执行发起人挂断操作');
                    }
                    $status = ($callRecord['status'] == VideoCallRecord::STATUS_INITIATED)
                        ? VideoCallRecord::STATUS_CANCELLED
                        : VideoCallRecord::STATUS_ENDED;
                    $endReason = ($callRecord['status'] == VideoCallRecord::STATUS_INITIATED)
                        ? VideoCallRecord::END_REASON_CANCELLED
                        : VideoCallRecord::END_REASON_NORMAL;
                    break;
                case 3: // 接听人挂断
                    if ($params['user_id'] != $callRecord['call_be_user_id']) {
                        throw new \Exception('只有接听人才能执行接听人挂断操作');
                    }
                    $status = ($callRecord['status'] == VideoCallRecord::STATUS_INITIATED)
                        ? VideoCallRecord::STATUS_REJECTED
                        : VideoCallRecord::STATUS_ENDED;
                    $endReason = ($callRecord['status'] == VideoCallRecord::STATUS_INITIATED)
                        ? VideoCallRecord::END_REASON_REJECTED
                        : VideoCallRecord::END_REASON_NORMAL;
                    break;
                case 4: // 系统挂断
                    $status = VideoCallRecord::STATUS_ABNORMAL;
                    $endReason = VideoCallRecord::END_REASON_ABNORMAL;
                    break;
                default:
                    throw new \Exception('无效的挂断类型');
            }

            $currentTime = time();

            // 计算通话时长（只有已接通的通话才计算时长）
            $duration = 0;
            if ($callRecord['status'] == VideoCallRecord::STATUS_CONNECTED && $callRecord['connect_time'] > 0) {
                $duration = $currentTime - $callRecord['connect_time'];
            }

            // 更新通话记录
            $updateData = [
                'status' => $status,
                'end_time' => $currentTime,
                'duration' => $duration,
                'hangup_user_id' => $params['user_id'],
                'end_reason' => $endReason,
                'update_time' => $currentTime
            ];

            $result = VideoCallRecord::where('id', $params['call_id'])->update($updateData);
            if (!$result) {
                throw new \Exception('更新通话记录失败');
            }

            // 删除缓存
            Cache::delete('video_call_' . $callRecord['channel_id']);

            // 发送IM通知给对方
            $toUserId = ($params['user_id'] == $callRecord['user_id'])
                ? $callRecord['call_be_user_id']
                : $callRecord['user_id'];

            self::sendHangupMessage($params['user_id'], $toUserId, $callRecord, $params['hangup_type']);

            Db::commit();

            LogService::write('通话挂断成功', [
                'call_id' => $params['call_id'],
                'hangup_user_id' => $params['user_id'],
                'hangup_type' => $params['hangup_type'],
                'status' => $status,
                'duration' => $duration
            ], 'video_call');

            return [
                'call_id' => $params['call_id'],
                'status' => $status,
                'duration' => $duration,
                'end_time' => $currentTime,
                'hangup_type' => $params['hangup_type']
            ];

        } catch (\Exception $e) {
            Db::rollback();

            LogService::write('挂断通话异常', [
                'call_id' => $params['call_id'] ?? 0,
                'hangup_user_id' => $params['user_id'] ?? 0,
                'hangup_type' => $params['hangup_type'] ?? 0,
                'error' => $e->getMessage()
            ], 'video_call_error');

            self::setError($e->getMessage());
            return false;
        }
    }

    /**
     * 发送挂断通知消息
     * @param int $fromUserId 发送者ID
     * @param int $toUserId 接收者ID
     * @param array $callRecord 通话记录
     * @param int $hangupType 挂断类型
     */
    private static function sendHangupMessage($fromUserId, $toUserId, $callRecord, $hangupType)
    {
        try {
            // 根据挂断类型确定消息类型
            $messageType = ImMessageEnum::ONE_ONE_HANG_UP;
            $messageContent = '通话已结束';

            switch ($hangupType) {
                case 1: // 超时
                    $messageType = ImMessageEnum::NOT_CONNECTED_DISCOMMECTED;
                    $messageContent = '通话超时未接听';
                    break;
                case 2: // 发起人挂断
                    $messageContent = ($callRecord['status'] == VideoCallRecord::STATUS_INITIATED)
                        ? '发起人取消了通话'
                        : '发起人结束了通话';
                    break;
                case 3: // 接听人挂断
                    $messageContent = ($callRecord['status'] == VideoCallRecord::STATUS_INITIATED)
                        ? '对方拒绝了通话'
                        : '对方结束了通话';
                    break;
                case 4: // 系统挂断
                    $messageContent = '系统结束了通话';
                    break;
            }

            $ext = [
                'type' => $messageType,
                'channel' => $callRecord['channel_id'],
                'call_id' => $callRecord['id'],
                'hangup_type' => $hangupType,
                'msg_content' => $messageContent,
                'sender' => [
                    'id' => $fromUserId,
                    'user_nickname' => '',
                    'avatar' => ''
                ]
            ];

            // 这里需要调用IM服务发送消息
            // send_im_custom_msg($fromUserId, $toUserId, $messageType, $ext, $messageContent);

        } catch (\Exception $e) {
            LogService::write('发送挂断通知失败', [
                'from_user_id' => $fromUserId,
                'to_user_id' => $toUserId,
                'call_id' => $callRecord['id'],
                'hangup_type' => $hangupType,
                'error' => $e->getMessage()
            ], 'video_call_error');
        }
    }
}
