<?php

namespace app\applent\logic\video;

use app\common\logic\BaseLogic;
use app\common\model\user\User;
use app\common\model\VideoCallRecord;
use app\common\service\agora\AgoraService;
use app\common\service\LogService;
use app\common\service\ConfigService;
use app\common\enum\ImMessageEnum;
use think\facade\Cache;
use think\facade\Db;
use app\common\model\user\UserBalance;
/**
 * 视频通话业务逻辑类
 */
class VideoCallLogic extends BaseLogic
{
    /**
     * 发起通话
     * @param array $params 通话参数
     * @return array|false
     */
    public static function startCall($params)
    {
        Db::startTrans();
        try {
            if($params['to_user_id'] == $params['user_id']){
                throw new \Exception('不能给自己发起通话');
            }

            // 检查用户状态
            if (VideoCallRecord::isUserInCall($params['user_id'])) {
                throw new \Exception('您正在通话中，无法发起新的通话');
            }

            if (VideoCallRecord::isUserInCall($params['to_user_id'])) {
                throw new \Exception('对方正在通话中，请稍后再试');
            }

            //查找接听人信息
            $toUserInfo = User::where('id', $params['to_user_id'])
                        ->field('id,sex,video_price,voice_price,is_service_staff,is_auth,is_disable')
                        ->find();
            if (!$toUserInfo) {
                throw new \Exception('您所拨打的用户不存在');
            }
            if($toUserInfo['is_disable']){
                throw new \Exception('该用户已禁用');
            }
            if(!$toUserInfo['is_service_staff'] && $toUserInfo['sex'] == 2 && $toUserInfo['is_auth'] != 1){
                throw new \Exception('对方未认证，无法发起通话');
            }
            //检查接听人每分钟价格
            $price = $params['call_type'] == 1 ? $toUserInfo['video_price'] : $toUserInfo['voice_price'];
            if($toUserInfo['is_service_staff']){
                $price = 0;
            }

            //查找发起人余额
            $userBalance = UserBalance::getUserBalance($params['user_id']);
            $beforeSenderBalance = $userBalance ? $userBalance['balance'] : 0;
            if ($price > $beforeSenderBalance) {
                throw new \Exception('余额不足，请先充值');
            }
            
            //生成频道ID和Token
            $agoraService = new AgoraService();
            $channelId = $agoraService->generateChannelId($params['user_id'], $params['to_user_id']);      //通道ID
            $callerToken = $agoraService->generateRtcToken($params['user_id'], $channelId, 'publisher');   //发起人Token
            $calleeToken = $agoraService->generateRtcToken($params['to_user_id'], $channelId, 'publisher');//接听人Token

            // 创建通话记录
            $callRecord = [
                'user_id'           => $params['user_id'],
                'call_be_user_id'   => $params['to_user_id'],
                'channel_id'        => $channelId,
                'status'            => 0,
                'type'              => $params['call_type'],  
                'duration'          => 0,
                'unit_price'        => $price,
                'ip'                => request()->ip(),
                'create_time'       =>time(),
                'end_time'          => 0,
            ];
            $callRecordId = VideoCallRecord::insertGetId($callRecord);
            if (!$callRecordId) {
                return self::setError('创建通话记录失败');
            }
            
            var_dump($callRecordId);die;
            // 6. 发送IM通话邀请消息
            // self::sendCallInviteMessage(
            //     $params['user_id'],
            //     $params['to_user_id'],
            //     $channelId,
            //     $params['call_type'],
            //     $callRecord->id
            // );

            Db::commit();

            return [
                'call_id' => $callRecord->id,
                'channel_id' => $channelId,
                'caller_token' => $callerToken,
                'callee_token' => $calleeToken,
                'caller_info' => [
                    'id' => $caller->id,
                    'nickname' => $caller->nickname,
                    'avatar' => $caller->avatar,
                ],
                'callee_info' => [
                    'id' => $callee->id,
                    'nickname' => $callee->nickname,
                    'avatar' => $callee->avatar,
                ],
                'price' => $price,
            ];

        } catch (\Exception $e) {
            Db::rollback();

            LogService::write('发起通话异常', [
                'caller_id' => $params['user_id'] ?? 0,
                'callee_id' => $params['to_user_id'] ?? 0,
                'error' => $e->getMessage()
            ], 'video_call_error');

            self::setError($e->getMessage());
            return false;
        }
    }
}
