<?php

namespace app\applent\logic\video;

use app\common\logic\BaseLogic;
use app\common\model\user\User;
use app\common\model\VideoCallRecord;
use app\common\service\agora\AgoraService;
use app\common\service\LogService;
use app\common\service\ConfigService;
use app\common\enum\ImMessageEnum;
use think\facade\Cache;
use think\facade\Db;
use app\common\model\user\UserBalance;
/**
 * 视频通话业务逻辑类
 */
class VideoCallLogic extends BaseLogic
{
    /**
     * 发起通话
     * @param array $params 通话参数
     * @return array|false
     */
    public static function startCall($params)
    {
        Db::startTrans();
        try {
            if($params['to_user_id'] == $params['user_id']){
                throw new \Exception('不能给自己发起通话');
            }

            // 检查用户状态
            if (VideoCallRecord::isUserInCall($params['user_id'])) {
                throw new \Exception('您正在通话中，无法发起新的通话');
            }

            if (VideoCallRecord::isUserInCall($params['to_user_id'])) {
                throw new \Exception('对方正在通话中，请稍后再试');
            }

            //查找接听人信息
            $toUserInfo = User::where('id', $params['to_user_id'])
                        ->field('id,sex,video_price,voice_price,is_service_staff,is_auth,is_disable')
                        ->find();
            if (!$toUserInfo) {
                throw new \Exception('您所拨打的用户不存在');
            }
            if($toUserInfo['is_disable']){
                throw new \Exception('该用户已禁用');
            }
            if(!$toUserInfo['is_service_staff'] && $toUserInfo['sex'] == 2 && $toUserInfo['is_auth'] != 1){
                throw new \Exception('对方未认证，无法发起通话');
            }
            //检查接听人每分钟价格
            $price = $params['call_type'] == 1 ? $toUserInfo['video_price'] : $toUserInfo['voice_price'];
            if($toUserInfo['is_service_staff']){
                $price = 0;
            }

            //查找发起人余额
            $userBalance = UserBalance::getUserBalance($params['user_id']);
            $beforeSenderBalance = $userBalance ? $userBalance['balance'] : 0;
            if ($price > $beforeSenderBalance) {
                throw new \Exception('余额不足，请先充值');
            }
            var_dump($price);die;
            //生成频道ID和Token
            $agoraService = new AgoraService();
            $channelId = $agoraService->generateChannelId($params['user_id'], $params['to_user_id']);

            $callerToken = $agoraService->generateRtcToken($params['user_id'], $channelId, 'publisher');
            $calleeToken = $agoraService->generateRtcToken($params['to_user_id'], $channelId, 'publisher');

            // 5. 创建通话记录
            $callRecord = VideoCallRecord::createCall(
                $params['user_id'],
                $params['to_user_id'],
                $channelId,
                $params['call_type']
            );

            if (!$callRecord) {
                return self::setError('创建通话记录失败');
            }

            // 6. 发送IM通话邀请消息
            self::sendCallInviteMessage(
                $params['user_id'],
                $params['to_user_id'],
                $channelId,
                $params['call_type'],
                $callRecord->id
            );

            // 7. 缓存通话信息
            $callInfo = [
                'call_id' => $callRecord->id,
                'channel_id' => $channelId,
                'caller_id' => $params['user_id'],
                'callee_id' => $params['to_user_id'],
                'type' => $params['call_type'],
                'status' => VideoCallRecord::STATUS_INITIATED,
                'create_time' => time(),
            ];

            Cache::set('video_call_' . $channelId, $callInfo, 3600); // 缓存1小时

            Db::commit();

            LogService::write('发起通话', [
                'caller_id' => $params['user_id'],
                'callee_id' => $params['to_user_id'],
                'channel_id' => $channelId,
                'type' => $params['call_type'],
                'call_id' => $callRecord->id
            ], 'video_call');

            return [
                'call_id' => $callRecord->id,
                'channel_id' => $channelId,
                'caller_token' => $callerToken,
                'callee_token' => $calleeToken,
                'caller_info' => [
                    'id' => $caller->id,
                    'nickname' => $caller->nickname,
                    'avatar' => $caller->avatar,
                ],
                'callee_info' => [
                    'id' => $callee->id,
                    'nickname' => $callee->nickname,
                    'avatar' => $callee->avatar,
                ],
                'charge_info' => $chargeInfo,
                'agora_config' => $agoraService->getConfig(),
            ];

        } catch (\Exception $e) {
            Db::rollback();

            LogService::write('发起通话异常', [
                'caller_id' => $params['user_id'] ?? 0,
                'callee_id' => $params['to_user_id'] ?? 0,
                'error' => $e->getMessage()
            ], 'video_call_error');

            self::setError($e->getMessage());
            return false;
        }
    }

    /**
     * 接受通话
     * @param int $userId 用户ID
     * @param string $channelId 频道ID
     * @return array|false
     */
    public static function acceptCall($userId, $channelId)
    {
        try {
            // 1. 获取通话记录
            $callRecord = VideoCallRecord::getCallByChannel($channelId);
            if (!$callRecord) {
                return self::setError('通话记录不存在');
            }

            // 2. 验证用户权限
            if ($callRecord->call_be_user_id != $userId) {
                return self::setError('无权限操作此通话');
            }

            // 3. 检查通话状态
            if ($callRecord->status != VideoCallRecord::STATUS_INITIATED) {
                return self::setError('通话状态异常，无法接受');
            }

            // 4. 更新通话状态
            $callRecord->updateStatus(VideoCallRecord::STATUS_CONNECTED);

            // 5. 发送IM接受消息
            self::sendCallAcceptMessage($callRecord->user_id, $userId, $channelId, $callRecord->id);

            // 6. 开始计费（如果需要）
            self::startCharging($callRecord);

            LogService::write('接受通话', [
                'user_id' => $userId,
                'channel_id' => $channelId,
                'call_id' => $callRecord->id
            ], 'video_call');

            return [
                'call_id' => $callRecord->id,
                'status' => 'accepted',
                'message' => '通话已接受'
            ];

        } catch (\Exception $e) {
            LogService::write('接受通话异常', [
                'user_id' => $userId,
                'channel_id' => $channelId,
                'error' => $e->getMessage()
            ], 'video_call_error');

            return self::setError('接受通话失败');
        }
    }

    /**
     * 拒绝通话
     * @param int $userId 用户ID
     * @param string $channelId 频道ID
     * @return array|false
     */
    public static function rejectCall($userId, $channelId)
    {
        try {
            $callRecord = VideoCallRecord::getCallByChannel($channelId);
            if (!$callRecord) {
                return self::setError('通话记录不存在');
            }

            if ($callRecord->call_be_user_id != $userId) {
                return self::setError('无权限操作此通话');
            }

            // 更新通话状态
            $callRecord->updateStatus(VideoCallRecord::STATUS_REJECTED);

            // 发送IM拒绝消息
            self::sendCallRejectMessage($callRecord->user_id, $userId, $channelId, $callRecord->id);

            // 清除缓存
            Cache::delete('video_call_' . $channelId);

            LogService::write('拒绝通话', [
                'user_id' => $userId,
                'channel_id' => $channelId,
                'call_id' => $callRecord->id
            ], 'video_call');

            return [
                'call_id' => $callRecord->id,
                'status' => 'rejected',
                'message' => '通话已拒绝'
            ];

        } catch (\Exception $e) {
            LogService::write('拒绝通话异常', [
                'user_id' => $userId,
                'channel_id' => $channelId,
                'error' => $e->getMessage()
            ], 'video_call_error');

            return self::setError('拒绝通话失败');
        }
    }

    /**
     * 挂断通话
     * @param int $userId 用户ID
     * @param string $channelId 频道ID
     * @return array|false
     */
    public static function hangupCall($userId, $channelId)
    {
        try {
            $callRecord = VideoCallRecord::getCallByChannel($channelId);
            if (!$callRecord) {
                return self::setError('通话记录不存在');
            }

            // 验证用户权限
            if ($callRecord->user_id != $userId && $callRecord->call_be_user_id != $userId) {
                return self::setError('无权限操作此通话');
            }

            // 更新通话状态
            $callRecord->updateStatus(VideoCallRecord::STATUS_ENDED);

            // 计算通话时长和费用，执行最终结算
            $settlementResult = self::processCallSettlement($callRecord);

            // 发送IM挂断消息
            $otherUserId = ($callRecord->user_id == $userId) ? $callRecord->call_be_user_id : $callRecord->user_id;
            self::sendCallHangupMessage($userId, $otherUserId, $channelId, $callRecord->id);

            // 清除缓存
            Cache::delete('video_call_' . $channelId);

            LogService::write('挂断通话', [
                'user_id' => $userId,
                'channel_id' => $channelId,
                'call_id' => $callRecord->id,
                'duration' => $callRecord->duration
            ], 'video_call');

            return [
                'call_id' => $callRecord->id,
                'status' => 'ended',
                'duration' => $settlementResult['duration'] ?? 0,
                'duration_minutes' => $settlementResult['duration_minutes'] ?? 0,
                'total_cost' => $settlementResult['total_cost'] ?? 0,
                'callee_earnings' => $settlementResult['callee_earnings'] ?? 0,
                'message' => '通话已结束'
            ];

        } catch (\Exception $e) {
            LogService::write('挂断通话异常', [
                'user_id' => $userId,
                'channel_id' => $channelId,
                'error' => $e->getMessage()
            ], 'video_call_error');

            return self::setError('挂断通话失败');
        }
    }

    /**
     * 计算通话费用
     * @param int $to_user_id 被叫用户ID
     * @param int $type 通话类型 1=视频 2=语音
     * @return array
     */
    private static function calculateCharge($to_user_id, $type)
    {
        $to_user_info  = User::where('id', $to_user_id)->field('video_price,voice_price')->find();
        // 获取被叫用户的通话价格
        $price = 0;
        if ($type == 1) {
            $price = $to_user_info->video_price > 0 ? $to_user_info->video_price : ConfigService::get('systemconfig', 'min_video_price', 50);
        } elseif ($type == 2) {
            $price = $to_user_info->voice_price > 0 ? $to_user_info->voice_price : ConfigService::get('systemconfig', 'min_voice_price', 50);
        }
        return $price;
    }

    /**
     * 发送通话邀请IM消息
     */
    private static function sendCallInviteMessage($fromUserId, $toUserId, $channelId, $type, $callId)
    {
        $messageType = ($type == VideoCallRecord::TYPE_VIDEO) ? ImMessageEnum::VIDEO_CALL_INVITE : ImMessageEnum::VOICE_CALL_INVITE;

        $data = [
            'channel_id' => $channelId,
            'call_id' => $callId,
            'call_type' => $type,
        ];

        return send_im_custom_msg($fromUserId, $toUserId, $messageType, $data, '通话邀请');
    }

    /**
     * 发送通话接受IM消息
     */
    private static function sendCallAcceptMessage($fromUserId, $toUserId, $channelId, $callId)
    {
        $data = [
            'channel_id' => $channelId,
            'call_id' => $callId,
        ];

        return send_im_custom_msg($toUserId, $fromUserId, ImMessageEnum::CALL_ACCEPT, $data, '通话已接受');
    }

    /**
     * 发送通话拒绝IM消息
     */
    private static function sendCallRejectMessage($fromUserId, $toUserId, $channelId, $callId)
    {
        $data = [
            'channel_id' => $channelId,
            'call_id' => $callId,
        ];

        return send_im_custom_msg($toUserId, $fromUserId, ImMessageEnum::CALL_REJECT, $data, '通话已拒绝');
    }

    /**
     * 发送通话挂断IM消息
     */
    private static function sendCallHangupMessage($fromUserId, $toUserId, $channelId, $callId)
    {
        $data = [
            'channel_id' => $channelId,
            'call_id' => $callId,
        ];

        return send_im_custom_msg($fromUserId, $toUserId, ImMessageEnum::CALL_HANGUP, $data, '通话已结束');
    }

    /**
     * 开始计费
     */
    private static function startCharging($callRecord)
    {
        // 这里实现计费逻辑
        // 可以使用定时任务或者队列来处理按分钟计费
    }

    /**
     * 处理通话结算（挂断时执行）
     */
    private static function processCallSettlement($callRecord)
    {
        try {
            $result = [
                'duration' => 0,
                'duration_minutes' => 0,
                'total_cost' => 0,
                'callee_earnings' => 0,
                'platform_earnings' => 0
            ];

            // 只有连接状态的通话才需要结算
            if ($callRecord->status != VideoCallRecord::STATUS_CONNECTED) {
                return $result;
            }

            // 计算通话时长
            $startTime = strtotime($callRecord->connect_time ?: $callRecord->create_time);
            $endTime = time();
            $duration = $endTime - $startTime;

            if ($duration <= 0) {
                return $result;
            }

            // 获取用户信息
            $caller = User::find($callRecord->user_id);
            $callee = User::find($callRecord->call_be_user_id);

            if (!$caller || !$callee) {
                LogService::write('结算失败：用户不存在', [
                    'call_id' => $callRecord->id,
                    'caller_id' => $callRecord->user_id,
                    'callee_id' => $callRecord->call_be_user_id
                ], 'video_call_error');
                return $result;
            }

            // 计算费用
            $chargeInfo = self::calculateCharge($caller, $callee, $callRecord->type);
            $pricePerMinute = $chargeInfo['price'];

            // 按分钟计费，不足1分钟按1分钟计算
            $minutes = ceil($duration / 60);
            $totalCost = $minutes * $pricePerMinute;

            // 检查余额是否充足
            if ($caller->user_money < $totalCost) {
                // 余额不足，按实际余额计算
                $totalCost = $caller->user_money;
                LogService::write('通话结算：余额不足', [
                    'call_id' => $callRecord->id,
                    'required_cost' => $minutes * $pricePerMinute,
                    'actual_cost' => $totalCost,
                    'user_balance' => $caller->user_money
                ], 'video_call');
            }

            // 计算平台抽成和接听方收益
            $platformRate = config('video_call.platform_rate', 0.3); // 平台抽成30%
            $platformEarnings = $totalCost * $platformRate;
            $calleeEarnings = $totalCost - $platformEarnings;

            // 开始数据库事务
            Db::startTrans();

            try {
                // 扣除主叫方费用
                if ($totalCost > 0) {
                    $caller->user_money -= $totalCost;
                    $caller->save();

                    // 记录主叫方消费记录
                    self::createUserMoneyLog($caller->id, -$totalCost, '视频通话消费', [
                        'call_id' => $callRecord->id,
                        'duration' => $duration,
                        'minutes' => $minutes,
                        'price_per_minute' => $pricePerMinute
                    ]);
                }

                // 增加被叫方收益
                if ($calleeEarnings > 0) {
                    $callee->user_money += $calleeEarnings;
                    $callee->save();

                    // 记录被叫方收益记录
                    self::createUserMoneyLog($callee->id, $calleeEarnings, '视频通话收益', [
                        'call_id' => $callRecord->id,
                        'duration' => $duration,
                        'minutes' => $minutes,
                        'total_cost' => $totalCost,
                        'platform_rate' => $platformRate
                    ]);
                }

                // 更新通话记录
                $callRecord->duration = $duration;
                $callRecord->total_cost = $totalCost;
                $callRecord->callee_earnings = $calleeEarnings;
                $callRecord->platform_earnings = $platformEarnings;
                $callRecord->end_time = date('Y-m-d H:i:s');
                $callRecord->save();

                // 提交事务
                Db::commit();

                LogService::write('通话结算成功', [
                    'call_id' => $callRecord->id,
                    'duration' => $duration,
                    'total_cost' => $totalCost,
                    'callee_earnings' => $calleeEarnings,
                    'platform_earnings' => $platformEarnings
                ], 'video_call');

                return [
                    'duration' => $duration,
                    'duration_minutes' => $minutes,
                    'total_cost' => $totalCost,
                    'callee_earnings' => $calleeEarnings,
                    'platform_earnings' => $platformEarnings
                ];

            } catch (\Exception $e) {
                // 回滚事务
                Db::rollback();
                throw $e;
            }

        } catch (\Exception $e) {
            LogService::write('通话结算异常', [
                'call_id' => $callRecord->id ?? 0,
                'error' => $e->getMessage()
            ], 'video_call_error');

            return [
                'duration' => 0,
                'duration_minutes' => 0,
                'total_cost' => 0,
                'callee_earnings' => 0,
                'platform_earnings' => 0
            ];
        }
    }

    /**
     * 创建用户资金变动记录
     */
    private static function createUserMoneyLog($userId, $amount, $description, $extra = [])
    {
        // 这里需要根据项目的用户资金记录表结构来实现
        // 假设有一个 la_user_money_log 表
        try {
            Db::name('user_money_log')->insert([
                'user_id' => $userId,
                'amount' => $amount,
                'description' => $description,
                'extra_data' => json_encode($extra),
                'create_time' => date('Y-m-d H:i:s')
            ]);
        } catch (\Exception $e) {
            LogService::write('创建资金记录失败', [
                'user_id' => $userId,
                'amount' => $amount,
                'error' => $e->getMessage()
            ], 'video_call_error');
        }
    }

    /**
     * 生成声网Token（兼容旧方法）
     */
    public static function generateAgoraToken($uid, $channelName, $appId = null, $certificate = null)
    {
        try {
            $agoraService = new AgoraService();
            return $agoraService->generateRtcToken($uid, $channelName);
        } catch (\Exception $e) {
            LogService::write('生成Token失败', [
                'uid' => $uid,
                'channel' => $channelName,
                'error' => $e->getMessage()
            ], 'agora_error');

            return false;
        }
    }

    /**
     * 获取用户通话记录
     */
    public static function getUserCallRecords($params)
    {
        $userId = $params['user_id'];
        $page = $params['page'] ?? 1;
        $limit = $params['limit'] ?? 20;

        // 限制每页数量
        if ($limit > 50) {
            $limit = 50;
        }

        $records = VideoCallRecord::getUserCallRecords($userId, $page, $limit);

        return [
            'list' => $records,
            'page' => $page,
            'limit' => $limit,
        ];
    }

    /**
     * 获取通话统计
     */
    public static function getCallStats($params)
    {
        $userId = $params['user_id'];
        $startTime = $params['start_time'] ?? null;
        $endTime = $params['end_time'] ?? null;

        return VideoCallRecord::getCallStats($userId, $startTime, $endTime);
    }

    /**
     * 获取声网Token
     */
    public static function getAgoraToken($userId, $channelId, $role = 'attendee')
    {
        try {
            $agoraService = new AgoraService();
            $token = $agoraService->generateRtcToken($userId, $channelId, $role);

            return [
                'token' => $token,
                'uid' => $userId,
                'channel_id' => $channelId,
                'expire_time' => time() + 86400 * 30, // 30天后过期
            ];

        } catch (\Exception $e) {
            LogService::write('获取Token失败', [
                'user_id' => $userId,
                'channel_id' => $channelId,
                'error' => $e->getMessage()
            ], 'agora_error');

            return self::setError('Token获取失败：' . $e->getMessage());
        }
    }

    /**
     * 获取声网配置
     */
    public static function getAgoraConfig()
    {
        try {
            $agoraService = new AgoraService();
            return $agoraService->getConfig();

        } catch (\Exception $e) {
            LogService::write('获取声网配置失败', [
                'error' => $e->getMessage()
            ], 'agora_error');

            return self::setError('配置获取失败：' . $e->getMessage());
        }
    }

    /**
     * 通话质量上报
     */
    public static function reportQuality($params)
    {
        try {
            $agoraService = new AgoraService();
            $result = $agoraService->recordQualityData(
                $params['channel_id'],
                $params['user_id'],
                $params['quality_data']
            );

            return $result;

        } catch (\Exception $e) {
            LogService::write('通话质量上报失败', [
                'user_id' => $params['user_id'],
                'channel_id' => $params['channel_id'] ?? '',
                'error' => $e->getMessage()
            ], 'agora_error');

            return self::setError('质量数据上报失败：' . $e->getMessage());
        }
    }

    /**
     * 检查用户通话状态
     */
    public static function checkCallStatus($userId)
    {
        $activeCall = VideoCallRecord::getActiveCall($userId);

        if ($activeCall) {
            return [
                'in_call' => true,
                'call_info' => [
                    'call_id' => $activeCall->id,
                    'channel_id' => $activeCall->channel_id,
                    'type' => $activeCall->type,
                    'status' => $activeCall->status,
                    'start_time' => $activeCall->start_time,
                ]
            ];
        } else {
            return [
                'in_call' => false,
                'call_info' => null
            ];
        }
    }

    /**
     * 获取通话实时费用信息（用于前端轮询）
     */
    public static function getCallCostInfo($params)
    {
        try {
            $channelId = $params['channel_id'];
            $userId = $params['user_id'];

            // 获取通话记录
            $callRecord = VideoCallRecord::getCallByChannel($channelId);
            if (!$callRecord) {
                return self::setError('通话记录不存在');
            }

            // 验证用户权限（只有通话双方可以查询）
            if ($callRecord->user_id != $userId && $callRecord->call_be_user_id != $userId) {
                return self::setError('无权限查询此通话信息');
            }

            // 检查通话是否已连接
            if ($callRecord->status != VideoCallRecord::STATUS_CONNECTED) {
                return [
                    'is_connected' => false,
                    'status' => $callRecord->status,
                    'message' => '通话未连接'
                ];
            }

            // 计算通话时长（秒）
            $startTime = strtotime($callRecord->connect_time ?: $callRecord->create_time);
            $currentTime = time();
            $duration = $currentTime - $startTime;

            // 获取用户信息
            $caller = User::find($callRecord->user_id);
            $callee = User::find($callRecord->call_be_user_id);

            if (!$caller || !$callee) {
                return self::setError('用户信息不存在');
            }

            // 计算费用信息
            $chargeInfo = self::calculateCharge($caller, $callee, $callRecord->type);
            $pricePerMinute = $chargeInfo['price']; // 每分钟价格

            // 计算已产生的费用（按秒计算，向上取整到分钟）
            $minutes = ceil($duration / 60);
            $totalCost = $minutes * $pricePerMinute;

            // 计算剩余余额
            $remainingBalance = $caller->user_money - $totalCost;
            $isBalanceSufficient = $remainingBalance >= $pricePerMinute; // 至少够下一分钟

            // 计算接听方收益（扣除平台抽成）
            $platformRate = config('video_call.platform_rate', 0.3); // 平台抽成比例，默认30%
            $calleeEarnings = $totalCost * (1 - $platformRate);

            return [
                'is_connected' => true,
                'call_info' => [
                    'call_id' => $callRecord->id,
                    'channel_id' => $channelId,
                    'duration' => $duration, // 通话时长（秒）
                    'duration_minutes' => $minutes, // 通话时长（分钟，向上取整）
                    'type' => $callRecord->type,
                    'start_time' => $callRecord->connect_time ?: $callRecord->create_time,
                ],
                'cost_info' => [
                    'price_per_minute' => $pricePerMinute, // 每分钟价格
                    'total_cost' => $totalCost, // 已产生总费用
                    'platform_rate' => $platformRate, // 平台抽成比例
                ],
                'caller_info' => [
                    'user_id' => $caller->id,
                    'nickname' => $caller->nickname,
                    'original_balance' => $caller->user_money, // 原始余额
                    'remaining_balance' => $remainingBalance, // 剩余余额
                    'is_balance_sufficient' => $isBalanceSufficient, // 余额是否充足
                    'warning_threshold' => $pricePerMinute * 2, // 余额警告阈值（够2分钟）
                ],
                'callee_info' => [
                    'user_id' => $callee->id,
                    'nickname' => $callee->nickname,
                    'current_earnings' => $calleeEarnings, // 当前预计收益
                    'earnings_per_minute' => $pricePerMinute * (1 - $platformRate), // 每分钟收益
                ],
                'warnings' => self::getBalanceWarnings($remainingBalance, $pricePerMinute)
            ];

        } catch (\Exception $e) {
            LogService::write('获取通话费用信息失败', [
                'user_id' => $params['user_id'] ?? 0,
                'channel_id' => $params['channel_id'] ?? '',
                'error' => $e->getMessage()
            ], 'video_call_error');

            return self::setError('获取费用信息失败：' . $e->getMessage());
        }
    }

    /**
     * 获取余额警告信息
     */
    private static function getBalanceWarnings($remainingBalance, $pricePerMinute)
    {
        $warnings = [];

        if ($remainingBalance <= 0) {
            $warnings[] = [
                'type' => 'danger',
                'message' => '余额不足，通话即将结束'
            ];
        } elseif ($remainingBalance < $pricePerMinute) {
            $warnings[] = [
                'type' => 'warning',
                'message' => '余额不足1分钟通话费用'
            ];
        } elseif ($remainingBalance < $pricePerMinute * 2) {
            $warnings[] = [
                'type' => 'info',
                'message' => '余额较低，建议及时充值'
            ];
        }

        return $warnings;
    }

    /**
     * 处理异常结算（用于异常断开的通话）
     */
    public static function processAbnormalSettlement($callRecord)
    {
        try {
            // 获取用户信息
            $caller = User::find($callRecord->user_id);
            $callee = User::find($callRecord->call_be_user_id);

            if (!$caller || !$callee) {
                return false;
            }

            // 计算费用
            $chargeInfo = self::calculateCharge($caller, $callee, $callRecord->type);
            $pricePerMinute = $chargeInfo['price'];

            // 按分钟计费，不足1分钟按1分钟计算
            $minutes = ceil($callRecord->duration / 60);
            $totalCost = $minutes * $pricePerMinute;

            // 检查余额是否充足
            if ($caller->user_money < $totalCost) {
                $totalCost = $caller->user_money; // 按实际余额计算
            }

            // 计算平台抽成和接听方收益
            $platformRate = config('video_call.platform_rate', 0.3);
            $platformEarnings = $totalCost * $platformRate;
            $calleeEarnings = $totalCost - $platformEarnings;

            // 开始数据库事务
            Db::startTrans();

            try {
                // 扣除主叫方费用
                if ($totalCost > 0) {
                    $caller->user_money -= $totalCost;
                    $caller->save();

                    // 记录主叫方消费记录
                    self::createUserMoneyLog($caller->id, -$totalCost, '视频通话消费（异常结束）', [
                        'call_id' => $callRecord->id,
                        'duration' => $callRecord->duration,
                        'minutes' => $minutes,
                        'end_reason' => 'abnormal'
                    ]);
                }

                // 增加被叫方收益
                if ($calleeEarnings > 0) {
                    $callee->user_money += $calleeEarnings;
                    $callee->save();

                    // 记录被叫方收益记录
                    self::createUserMoneyLog($callee->id, $calleeEarnings, '视频通话收益（异常结束）', [
                        'call_id' => $callRecord->id,
                        'duration' => $callRecord->duration,
                        'minutes' => $minutes,
                        'total_cost' => $totalCost,
                        'end_reason' => 'abnormal'
                    ]);
                }

                // 提交事务
                Db::commit();

                LogService::write('异常通话结算成功', [
                    'call_id' => $callRecord->id,
                    'duration' => $callRecord->duration,
                    'total_cost' => $totalCost,
                    'callee_earnings' => $calleeEarnings,
                    'end_reason' => 'abnormal'
                ], 'video_call');

                return [
                    'total_cost' => $totalCost,
                    'callee_earnings' => $calleeEarnings,
                    'platform_earnings' => $platformEarnings
                ];

            } catch (\Exception $e) {
                // 回滚事务
                Db::rollback();
                throw $e;
            }

        } catch (\Exception $e) {
            LogService::write('异常通话结算失败', [
                'call_id' => $callRecord->id ?? 0,
                'error' => $e->getMessage()
            ], 'video_call_error');

            return false;
        }
    }

    /**
     * 心跳检测（用户在通话中定期调用）
     */
    public static function heartbeat($params)
    {
        try {
            $channelId = $params['channel_id'];
            $userId = $params['user_id'];

            $callRecord = VideoCallRecord::getCallByChannel($channelId);
            if (!$callRecord) {
                return self::setError('通话记录不存在');
            }

            // 验证用户权限
            if ($callRecord->user_id != $userId && $callRecord->call_be_user_id != $userId) {
                return self::setError('无权限操作此通话');
            }

            // 只有连接状态的通话才需要心跳
            if ($callRecord->status != VideoCallRecord::STATUS_CONNECTED) {
                return self::setError('通话未连接');
            }

            // 更新心跳时间
            $callRecord->updateHeartbeat($userId);

            return [
                'heartbeat_time' => date('Y-m-d H:i:s'),
                'status' => 'success'
            ];

        } catch (\Exception $e) {
            LogService::write('心跳检测失败', [
                'user_id' => $params['user_id'] ?? 0,
                'channel_id' => $params['channel_id'] ?? '',
                'error' => $e->getMessage()
            ], 'video_call_error');

            return self::setError('心跳检测失败');
        }
    }

}
