<?php

namespace app\applent\validate\video;

use app\common\validate\BaseValidate;
use app\common\model\user\User;
use app\common\model\VideoCallRecord;
/**
 * 视频通话验证器
 */
class VideoCallValidate extends BaseValidate
{
    protected $rule = [
        // 发起通话
        'to_user_id'    => 'require',
        'call_type'     => 'require|in:1,2',

        // 通话操作
        'channel_id'    => 'require|alphaNum|length:10,50',
        'channel_name'  => 'require|max:64',

        // 获取Token
        'role'          => 'in:attendee,publisher,subscriber,admin',

        // 质量上报
        'quality_data'  => 'require|array|checkQualityData',

        // 分页参数
        'page'          => 'integer|egt:1',
        'limit'         => 'integer|between:1,50',

        // 时间参数
        'start_time'    => 'date',
        'end_time'      => 'date',

        // 其他
        'abnormal_code' => 'number'
    ];

    protected $message = [
        'to_user_id.require'    => '请选择通话对象',

        'call_type.require'     => '请选择通话类型',
        'call_type.in'          => '通话类型只能是1(视频)或2(语音)',

        'channel_id.require'    => '频道ID不能为空',
        'channel_id.alphaNum'   => '频道ID格式错误',
        'channel_id.length'     => '频道ID长度必须在10-50个字符之间',

        'channel_name.require'  => '频道名称不能为空',
        'channel_name.max'      => '频道名称不能超过64个字符',

        'role.in'               => '角色类型错误',

        'quality_data.require'  => '质量数据不能为空',
        'quality_data.array'    => '质量数据格式错误',

        'page.integer'          => '页码必须是整数',
        'page.egt'              => '页码必须大于等于1',

        'limit.integer'         => '每页数量必须是整数',
        'limit.between'         => '每页数量必须在1-50之间',

        'start_time.date'       => '开始时间格式错误',
        'end_time.date'         => '结束时间格式错误',

        'abnormal_code.number'  => '异常代码必须是数字',
    ];

    protected $scene = [
        // 发起通话场景
        'start_call' => ['to_user_id', 'call_type'],

        // 接受通话场景
        'accept_call' => ['channel_id'],

        // 拒绝通话场景
        'reject_call' => ['channel_id'],

        // 挂断通话场景
        'hangup_call' => ['channel_id'],

        // 获取Token场景
        'get_token' => ['channel_id', 'role'],

        // 质量上报场景
        'report_quality' => ['channel_id', 'quality_data'],

        // 获取通话记录场景
        'get_records' => ['page', 'limit'],

        // 获取通话统计场景
        'get_stats' => ['start_time', 'end_time'],

        // 获取通话费用信息场景
        'get_cost_info' => ['channel_id'],

        // 心跳检测场景
        'heartbeat' => ['channel_id'],

        // 兼容旧场景
        'start' => ['to_user_id', 'call_type'],
        'token' => ['channel_name'],
        'end' => ['channel_id', 'abnormal_code'],
        'charging' => ['channel_id'],
    ];

    /**
     * 自定义验证规则：检查质量数据格式
     */
    protected function checkQualityData($value, $rule, $data = [])
    {
        if (!is_array($value)) {
            return '质量数据必须是数组格式';
        }

        // 检查必需的质量数据字段
        $requiredFields = ['network_quality', 'audio_quality', 'video_quality'];

        foreach ($requiredFields as $field) {
            if (!isset($value[$field])) {
                return "缺少必需的质量数据字段：{$field}";
            }
        }

        // 检查质量值范围（1-6）
        foreach (['network_quality', 'audio_quality', 'video_quality'] as $field) {
            if (isset($value[$field]) && ($value[$field] < 1 || $value[$field] > 6)) {
                return "{$field}值必须在1-6之间";
            }
        }

        return true;
    }

    // 兼容旧方法
    public function sceneStart()
    {
        return $this->only(['to_user_id','call_type'])
            ->append('to_user_id', 'neq:user_id');
    }

    public function sceneToken()
    {
        return $this->only(['channel_name']);
    }

    public function sceneEnd()
    {
        return $this->only(['channel_id','abnormal_code']);
    }

    public function sceneCharging()
    {
        return $this->only(['channel_id']);
    }
}
