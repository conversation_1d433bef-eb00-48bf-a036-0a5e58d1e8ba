# 声网(Agora)集成指南

## 概述

本项目已成功集成声网(Agora) RTC SDK，实现了完整的音视频通话功能，包括：

- 1对1视频通话
- 1对1语音通话
- Token认证机制
- 通话记录管理
- 质量监控
- 计费系统
- IM消息集成

## 🏗️ 架构设计

### 核心组件

```
声网集成架构
├── 配置层 (config/agora.php)
├── 服务层 (AgoraService)
├── 业务逻辑层 (VideoCallLogic)
├── 控制器层 (VideoController)
├── 数据模型层 (VideoCallRecord)
├── 验证器层 (VideoCallValidate)
└── IM集成层 (ImMessageEnum)
```

### 数据库设计

- `la_video_call_record` - 通话记录表
- `la_video_call_quality` - 通话质量记录表
- `la_video_call_billing` - 通话计费记录表

## ⚙️ 配置说明

### 1. 声网配置文件

**文件位置**: `config/agora.php`

```php
return [
    // 声网基础配置
    'app_id' => env('agora.app_id', ''),
    'app_certificate' => env('agora.app_certificate', ''),
    
    // 云端录制配置
    'customer_id' => env('agora.customer_id', ''),
    'customer_certificate' => env('agora.customer_certificate', ''),
    
    // Token配置
    'token_expire_time' => 86400 * 30,  // 30天
    'privilege_expire_time' => 86400,   // 1天
    
    // 录制配置
    'recording' => [
        'enabled' => true,
        'storage_vendor' => 3,  // 腾讯云COS
        'file_types' => ['hls', 'mp4'],
    ],
];
```

### 2. 环境变量配置

在 `.env` 文件中添加：

```bash
# 声网配置
agora.app_id=YOUR_AGORA_APP_ID
agora.app_certificate=YOUR_AGORA_CERTIFICATE
agora.customer_id=YOUR_CUSTOMER_ID
agora.customer_certificate=YOUR_CUSTOMER_CERTIFICATE
```

## 🔧 核心功能

### 1. Token生成

**服务类**: `app\common\service\agora\AgoraService`

```php
// 生成RTC Token
$agoraService = new AgoraService();
$token = $agoraService->generateRtcToken($uid, $channelName, 'publisher');

// 生成RTM Token
$rtmToken = $agoraService->generateRtmToken($userId);
```

### 2. 通话管理

**业务逻辑**: `app\applent\logic\video\VideoCallLogic`

```php
// 发起通话
$result = VideoCallLogic::startCall([
    'user_id' => $callerId,
    'to_user_id' => $calleeId,
    'call_type' => VideoCallRecord::TYPE_VIDEO
]);

// 接受通话
$result = VideoCallLogic::acceptCall($userId, $channelId);

// 挂断通话
$result = VideoCallLogic::hangupCall($userId, $channelId);
```

### 3. 数据模型

**模型类**: `app\common\model\VideoCallRecord`

```php
// 创建通话记录
$callRecord = VideoCallRecord::createCall($callerId, $calleeId, $channelId, $type);

// 更新通话状态
$callRecord->updateStatus(VideoCallRecord::STATUS_CONNECTED);

// 检查用户是否在通话中
$inCall = VideoCallRecord::isUserInCall($userId);
```

## 📡 API接口

### 基础路径

所有API接口的基础路径为：`/api/video/`

### 接口列表

| 接口 | 方法 | 说明 |
|------|------|------|
| `/start_call` | POST | 发起通话 |
| `/accept_call` | POST | 接受通话 |
| `/reject_call` | POST | 拒绝通话 |
| `/hangup_call` | POST | 挂断通话 |
| `/get_token` | POST | 获取Token |
| `/config` | GET | 获取配置 |
| `/records` | GET | 通话记录 |
| `/stats` | GET | 通话统计 |
| `/report_quality` | POST | 质量上报 |
| `/check_status` | GET | 检查状态 |

### 接口示例

#### 发起通话

```bash
POST /api/video/start_call
Content-Type: application/json

{
    "to_user_id": 123,
    "call_type": 1
}
```

**响应**:
```json
{
    "code": 1,
    "msg": "通话发起成功",
    "data": {
        "call_id": 456,
        "channel_id": "1640995200_100_123_1234",
        "caller_token": "006abc...",
        "callee_token": "006def...",
        "caller_info": {
            "id": 100,
            "nickname": "用户A",
            "avatar": "avatar.jpg"
        },
        "callee_info": {
            "id": 123,
            "nickname": "用户B",
            "avatar": "avatar2.jpg"
        },
        "charge_info": {
            "need_charge": true,
            "price": 10,
            "unit": "钻石/分钟"
        },
        "agora_config": {
            "app_id": "your_app_id",
            "max_users": 17
        }
    }
}
```

#### 获取Token

```bash
POST /api/video/get_token
Content-Type: application/json

{
    "channel_id": "1640995200_100_123_1234",
    "role": "publisher"
}
```

**响应**:
```json
{
    "code": 1,
    "msg": "Token获取成功",
    "data": {
        "token": "006abc123...",
        "uid": 100,
        "channel_id": "1640995200_100_123_1234",
        "expire_time": 1643587200
    }
}
```

## 💬 IM消息集成

### 消息类型

项目已集成腾讯云IM，支持以下通话相关消息类型：

| 类型 | 常量 | 说明 |
|------|------|------|
| 10 | VIDEO_CALL_INVITE | 视频通话邀请 |
| 11 | VOICE_CALL_INVITE | 语音通话邀请 |
| 12 | CALL_ACCEPT | 通话接受 |
| 13 | CALL_REJECT | 通话拒绝 |
| 14 | CALL_HANGUP | 通话挂断 |
| 16 | CALL_TIMEOUT | 通话超时 |
| 17 | CALL_CANCEL | 通话取消 |

### 消息发送

```php
// 发送通话邀请
send_im_custom_msg($fromUserId, $toUserId, ImMessageEnum::VIDEO_CALL_INVITE, [
    'channel_id' => $channelId,
    'call_id' => $callId,
    'call_type' => $type
], '通话邀请');
```

## 🗄️ 数据库表结构

### 通话记录表

```sql
CREATE TABLE `la_video_call_record` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '发起用户ID',
  `call_be_user_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '被叫用户ID',
  `channel_id` varchar(100) NOT NULL DEFAULT '' COMMENT '声网频道ID',
  `status` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '通话状态',
  `type` tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '通话类型',
  `duration` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '通话时长(秒)',
  `charge_amount` decimal(10,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '扣费金额',
  `start_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '通话开始时间',
  `end_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '通话结束时间',
  `create_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
  `delete_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '删除时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_call_be_user_id` (`call_be_user_id`),
  KEY `idx_channel_id` (`channel_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='视频通话记录表';
```

### 状态说明

- **通话状态**: 0=发起通话, 1=通话接通, 2=通话结束, 3=通话被拒绝, 4=通话超时, 5=通话取消
- **通话类型**: 1=视频通话, 2=语音通话

## 🔒 安全机制

### 1. Token认证

- 所有通话都需要有效的Agora Token
- Token有效期为30天，可配置
- 支持不同角色权限控制

### 2. 用户验证

- 验证用户是否存在且有效
- 检查用户是否已在通话中
- 验证用户余额是否充足

### 3. 频率限制

- 通话发起有1分钟冷却时间
- 防止恶意频繁呼叫

## 📊 监控与统计

### 1. 通话质量监控

```php
// 上报质量数据
$agoraService->recordQualityData($channelId, $userId, [
    'network_quality' => 5,
    'audio_quality' => 4,
    'video_quality' => 5,
    'rtt' => 120,
    'packet_loss' => 0.5
]);
```

### 2. 通话统计

```php
// 获取用户通话统计
$stats = VideoCallRecord::getCallStats($userId, $startTime, $endTime);
// 返回：总通话次数、总时长、视频/语音通话分布等
```

## 🚀 部署说明

### 1. 生产环境配置

- 确保声网App ID和证书正确配置
- 配置Redis缓存服务
- 设置适当的日志级别
- 配置HTTPS证书（推荐）

### 2. 性能优化

- 启用Redis缓存
- 配置数据库连接池
- 设置适当的超时时间
- 监控服务器资源使用

### 3. 故障排查

- 检查声网配置是否正确
- 验证Token生成是否正常
- 查看日志文件排查错误
- 监控数据库连接状态

## 📝 开发注意事项

1. **Token管理**: Token有过期时间，需要在客户端实现自动刷新机制
2. **错误处理**: 所有API都有完善的错误处理和日志记录
3. **并发控制**: 使用Redis锁防止并发问题
4. **数据一致性**: 通话状态变更使用数据库事务保证一致性
5. **扩展性**: 架构支持后续功能扩展，如多人通话、录制等

## 🔗 相关文档

- [声网官方文档](https://docs.agora.io/cn)
- [腾讯云IM文档](https://cloud.tencent.com/document/product/269)
- [ThinkPHP框架文档](https://www.kancloud.cn/manual/thinkphp6_0)
