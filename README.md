# 社交直播系统

## 项目简介

基于ThinkPHP框架开发的社交直播系统，集成了语音视频通话、即时通讯、直播、礼物系统等功能。

## 🚀 快速开始

### 环境要求

- PHP 7.4+
- MySQL 5.7+
- Redis 5.0+
- Nginx/Apache
- SSL证书 (HTTPS必需)

### 安装依赖

```bash
# 安装Composer依赖
composer install

# 配置环境变量
cp .env.example .env
# 编辑 .env 文件配置数据库等信息
# 配置声网参数
agora.app_id=YOUR_AGORA_APP_ID
agora.app_certificate=YOUR_AGORA_CERTIFICATE
agora.customer_id=YOUR_CUSTOMER_ID
agora.customer_certificate=YOUR_CUSTOMER_CERTIFICATE
```

### 环境配置

```bash
# 配置数据库
mysql -u root -p < database/install.sql

# 导入视频通话相关表结构
mysql -u root -p your_database < database/migrations/create_video_call_record_table.sql

# 配置Redis
systemctl start redis

# 配置Nginx
cp nginx.conf.example /etc/nginx/sites-available/your-site
```

### 启动开发

```bash
# 启动PHP内置服务器 (开发环境)
php think run

# 或配置Nginx/Apache (生产环境)
```

### 测试声网集成

```bash
# 运行声网集成测试脚本
php test_agora_integration.php
```

如果所有测试通过，说明声网集成配置正确，可以开始使用音视频功能。

## 📖 功能说明

### ✅ 核心功能

- **用户系统**: 注册登录、个人资料、实名认证
- **语音视频通话**: 基于声网(Agora)的1对1音视频通话
  - 视频通话/语音通话
  - Token认证机制
  - 通话记录管理
  - 质量监控
  - 计费系统
  - IM消息集成
- **即时通讯**: 基于腾讯云IM的消息系统
- **直播功能**: 视频直播、语音直播
- **礼物系统**: 虚拟礼物、全局广播
- **动态系统**: 用户动态发布、点赞评论
- **充值系统**: 多种支付方式集成

### 🚧 扩展功能

- **多人通话**: 支持多人音视频会议
- **屏幕共享**: 在线教育场景支持
- **美颜滤镜**: 第三方美颜SDK集成
- **AI审核**: 内容智能审核

## 🛠️ 技术栈

### 后端技术

- **框架**: ThinkPHP 6.0
- **数据库**: MySQL 5.7+
- **缓存**: Redis 5.0+
- **队列**: Redis Queue

### 前端技术

- **Web端**: AgoraRTC Web SDK 4.18.3
- **移动端**: Agora Native SDK
- **管理后台**: Bootstrap + jQuery

### 第三方服务

- **音视频**: 声网(Agora) RTC SDK
  - RTC SDK (实时音视频通话)
  - RTM SDK (实时消息传输)
  - 云端录制服务
  - 推流到CDN服务
  - Token认证机制
- **即时通讯**: 腾讯云IM
- **云存储**: 腾讯云COS
- **CDN**: 腾讯云CDN
- **支付**: 微信支付、支付宝
- **短信**: 阿里云短信服务

## 📚 文档目录

### 核心文档

- [声网集成指南](./Docs/声网集成指南.md) - 声网SDK集成完整指南
- [语音视频实现文档](./Docs/语音视频实现文档.md) - 详细的音视频功能实现说明
- [前端集成指南](./Docs/前端集成指南.md) - Web端和移动端集成教程
- [部署配置指南](./Docs/部署配置指南.md) - 生产环境部署配置

### API文档

- [用户API](./Docs/API/用户API.md) - 用户相关接口文档
- [通话API](./Docs/API/通话API.md) - 音视频通话接口文档
- [消息API](./Docs/API/消息API.md) - IM消息接口文档

## 🏗️ 项目结构

```
manage-api/
├── app/                    # 应用目录
│   ├── applent/           # 主应用模块
│   │   ├── controller/    # 控制器
│   │   ├── logic/         # 业务逻辑层
│   │   ├── model/         # 数据模型
│   │   └── validate/      # 验证器
│   ├── common/            # 公共模块
│   └── im_common.php      # IM公共函数
├── demo/                  # 演示项目
│   ├── application/       # 演示应用
│   └── public/           # 演示前端
├── Docs/                  # 项目文档
├── config/                # 配置文件
├── database/              # 数据库文件
├── public/                # 入口文件
├── runtime/               # 运行时文件
└── vendor/                # 第三方依赖
```

## ⚙️ 配置说明

### 声网(Agora)配置

```php
// config/agora.php
return [
    'app_id' => 'YOUR_AGORA_APP_ID',
    'app_certificate' => 'YOUR_AGORA_CERTIFICATE',
    'customer_id' => 'YOUR_CUSTOMER_ID',
    'customer_certificate' => 'YOUR_CUSTOMER_CERTIFICATE',
];
```

### 腾讯云IM配置

```php
// config/tencent_im.php
return [
    'sdk_app_id' => 'YOUR_SDKAPPID',
    'identifier' => 'administrator',
    'secret_key' => 'YOUR_SECRET_KEY',
    'group_id' => '@FGC#FULLGROUPIDKEY',
];
```

### 数据库配置

```php
// config/database.php
return [
    'default' => 'mysql',
    'connections' => [
        'mysql' => [
            'type' => 'mysql',
            'hostname' => '127.0.0.1',
            'database' => 'your_database',
            'username' => 'your_username',
            'password' => 'your_password',
            'charset' => 'utf8mb4',
        ],
    ],
];
```

## 🔧 开发指南

### 代码规范

- 遵循PSR-4自动加载规范
- 使用驼峰命名法
- 添加详细的中文注释
- 每个函数不超过80行

### 数据库规范

- 表名使用 `la_` 前缀
- 字段名使用下划线命名
- 必须有 `create_time` 和 `update_time` 字段
- 添加适当的索引

### API规范

- 统一返回JSON格式
- 使用HTTP状态码
- 错误信息要明确
- 添加接口文档

## 🧪 测试

### 单元测试

```bash
# 运行单元测试
./vendor/bin/phpunit

# 运行特定测试
./vendor/bin/phpunit tests/VideoCallTest.php
```

### 功能测试

```bash
# 测试音视频通话
php think test:video_call

# 测试IM消息
php think test:im_message
```

## 📊 监控

### 系统监控

- 服务器资源监控
- 数据库性能监控
- Redis缓存监控
- 应用错误监控

### 业务监控

- 通话质量监控
- 用户活跃度统计
- 收入统计分析
- 异常行为检测

## 🚀 部署

### 开发环境

```bash
# 使用Docker部署
docker-compose up -d

# 或使用传统方式
php think run
```

### 生产环境

详见 [部署配置指南](./Docs/部署配置指南.md)

## 🤝 贡献

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 📞 联系我们

- 项目地址: [GitHub](https://github.com/your-repo)
- 问题反馈: [Issues](https://github.com/your-repo/issues)
- 技术支持: <EMAIL>

## 🙏 致谢

- [ThinkPHP](https://www.thinkphp.cn/) - 优秀的PHP框架
- [Agora](https://www.agora.io/) - 实时音视频云服务
- [腾讯云](https://cloud.tencent.com/) - 云服务提供商

---

**注意**: 
- 生产环境必须使用HTTPS
- 定期更新第三方SDK版本
- 做好数据备份和安全防护
