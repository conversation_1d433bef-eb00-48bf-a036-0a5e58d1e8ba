<?php

namespace tests;

use PHPUnit\Framework\TestCase;
use app\applent\logic\video\VideoCallLogic;
use app\common\model\VideoCallRecord;
use think\facade\Db;

/**
 * 视频通话挂断功能测试
 */
class VideoCallHangupTest extends TestCase
{
    protected $testCallId;
    protected $testUserId = 1001;
    protected $testToUserId = 1002;

    protected function setUp(): void
    {
        parent::setUp();
        
        // 创建测试通话记录
        $this->createTestCallRecord();
    }

    protected function tearDown(): void
    {
        // 清理测试数据
        if ($this->testCallId) {
            VideoCallRecord::where('id', $this->testCallId)->delete();
        }
        
        parent::tearDown();
    }

    /**
     * 创建测试通话记录
     */
    private function createTestCallRecord()
    {
        $callData = [
            'user_id' => $this->testUserId,
            'call_be_user_id' => $this->testToUserId,
            'channel_id' => 'test_channel_' . time(),
            'status' => VideoCallRecord::STATUS_INITIATED,
            'type' => VideoCallRecord::TYPE_VIDEO,
            'create_time' => time(),
            'update_time' => time(),
        ];

        $this->testCallId = VideoCallRecord::insertGetId($callData);
    }

    /**
     * 测试超时挂断
     */
    public function testTimeoutHangup()
    {
        $params = [
            'call_id' => $this->testCallId,
            'hangup_type' => 1, // 超时
            'user_id' => $this->testUserId
        ];

        $result = VideoCallLogic::hangupCall($params);
        
        $this->assertNotFalse($result, '超时挂断应该成功');
        $this->assertEquals($this->testCallId, $result['call_id']);
        $this->assertEquals(VideoCallRecord::STATUS_TIMEOUT, $result['status']);
        $this->assertEquals(0, $result['duration']); // 未接通，时长为0
    }

    /**
     * 测试发起人挂断（未接通）
     */
    public function testCallerHangupBeforeConnect()
    {
        $params = [
            'call_id' => $this->testCallId,
            'hangup_type' => 2, // 发起人挂断
            'user_id' => $this->testUserId
        ];

        $result = VideoCallLogic::hangupCall($params);
        
        $this->assertNotFalse($result, '发起人挂断应该成功');
        $this->assertEquals(VideoCallRecord::STATUS_CANCELLED, $result['status']);
    }

    /**
     * 测试接听人挂断（未接通）
     */
    public function testCalleeHangupBeforeConnect()
    {
        $params = [
            'call_id' => $this->testCallId,
            'hangup_type' => 3, // 接听人挂断
            'user_id' => $this->testToUserId
        ];

        $result = VideoCallLogic::hangupCall($params);
        
        $this->assertNotFalse($result, '接听人挂断应该成功');
        $this->assertEquals(VideoCallRecord::STATUS_REJECTED, $result['status']);
    }

    /**
     * 测试已接通后挂断
     */
    public function testHangupAfterConnect()
    {
        // 先将通话状态设为已接通
        VideoCallRecord::where('id', $this->testCallId)->update([
            'status' => VideoCallRecord::STATUS_CONNECTED,
            'connect_time' => time() - 60 // 1分钟前接通
        ]);

        $params = [
            'call_id' => $this->testCallId,
            'hangup_type' => 2, // 发起人挂断
            'user_id' => $this->testUserId
        ];

        $result = VideoCallLogic::hangupCall($params);
        
        $this->assertNotFalse($result, '已接通后挂断应该成功');
        $this->assertEquals(VideoCallRecord::STATUS_ENDED, $result['status']);
        $this->assertGreaterThan(0, $result['duration']); // 应该有通话时长
    }

    /**
     * 测试系统挂断
     */
    public function testSystemHangup()
    {
        $params = [
            'call_id' => $this->testCallId,
            'hangup_type' => 4, // 系统挂断
            'user_id' => $this->testUserId
        ];

        $result = VideoCallLogic::hangupCall($params);
        
        $this->assertNotFalse($result, '系统挂断应该成功');
        $this->assertEquals(VideoCallRecord::STATUS_ABNORMAL, $result['status']);
    }

    /**
     * 测试无效的通话ID
     */
    public function testInvalidCallId()
    {
        $params = [
            'call_id' => 99999, // 不存在的ID
            'hangup_type' => 1,
            'user_id' => $this->testUserId
        ];

        $result = VideoCallLogic::hangupCall($params);
        
        $this->assertFalse($result, '无效通话ID应该返回失败');
        $this->assertEquals('通话记录不存在', VideoCallLogic::getError());
    }

    /**
     * 测试权限验证
     */
    public function testPermissionCheck()
    {
        $params = [
            'call_id' => $this->testCallId,
            'hangup_type' => 1,
            'user_id' => 9999 // 非通话参与者
        ];

        $result = VideoCallLogic::hangupCall($params);
        
        $this->assertFalse($result, '非参与者挂断应该失败');
        $this->assertEquals('您没有权限挂断此通话', VideoCallLogic::getError());
    }

    /**
     * 测试已结束通话的挂断
     */
    public function testHangupEndedCall()
    {
        // 先将通话状态设为已结束
        VideoCallRecord::where('id', $this->testCallId)->update([
            'status' => VideoCallRecord::STATUS_ENDED
        ]);

        $params = [
            'call_id' => $this->testCallId,
            'hangup_type' => 1,
            'user_id' => $this->testUserId
        ];

        $result = VideoCallLogic::hangupCall($params);
        
        $this->assertFalse($result, '已结束的通话不能再次挂断');
        $this->assertEquals('该通话已结束，无法挂断', VideoCallLogic::getError());
    }

    /**
     * 测试发起人权限验证
     */
    public function testCallerPermissionCheck()
    {
        $params = [
            'call_id' => $this->testCallId,
            'hangup_type' => 2, // 发起人挂断
            'user_id' => $this->testToUserId // 但用接听人ID
        ];

        $result = VideoCallLogic::hangupCall($params);
        
        $this->assertFalse($result, '非发起人不能执行发起人挂断');
        $this->assertEquals('只有发起人才能执行发起人挂断操作', VideoCallLogic::getError());
    }

    /**
     * 测试接听人权限验证
     */
    public function testCalleePermissionCheck()
    {
        $params = [
            'call_id' => $this->testCallId,
            'hangup_type' => 3, // 接听人挂断
            'user_id' => $this->testUserId // 但用发起人ID
        ];

        $result = VideoCallLogic::hangupCall($params);
        
        $this->assertFalse($result, '非接听人不能执行接听人挂断');
        $this->assertEquals('只有接听人才能执行接听人挂断操作', VideoCallLogic::getError());
    }

    /**
     * 测试无效的挂断类型
     */
    public function testInvalidHangupType()
    {
        $params = [
            'call_id' => $this->testCallId,
            'hangup_type' => 99, // 无效类型
            'user_id' => $this->testUserId
        ];

        $result = VideoCallLogic::hangupCall($params);
        
        $this->assertFalse($result, '无效挂断类型应该失败');
        $this->assertEquals('无效的挂断类型', VideoCallLogic::getError());
    }
}
