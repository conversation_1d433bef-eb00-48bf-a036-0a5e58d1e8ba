# 语音视频系统部署配置指南

## 环境要求

### 1. 服务器环境

- **操作系统**: Linux (推荐 CentOS 7+ / Ubuntu 18+)
- **PHP版本**: PHP 7.4+
- **Web服务器**: Nginx 1.18+ / Apache 2.4+
- **数据库**: MySQL 5.7+ / MariaDB 10.3+
- **缓存**: Redis 5.0+
- **SSL证书**: 必须支持HTTPS (音视频功能要求)

### 2. 第三方服务

- **声网(Agora)**: 音视频通话服务
- **腾讯云IM**: 即时通讯服务
- **腾讯云COS**: 录制文件存储
- **CDN服务**: 静态资源加速 (可选)

## 声网(Agora)配置

### 1. 注册和创建项目

1. 访问 [声网控制台](https://console.agora.io/)
2. 注册开发者账号并完成实名认证
3. 创建新项目，选择"安全模式"
4. 获取 App ID 和 App Certificate

### 2. 项目配置

```php
// 在系统配置中添加声网参数
$config = [
    // 声网配置
    'app_agora_key'     => 'YOUR_AGORA_APP_ID',        // 必填
    'app_certificate'   => 'YOUR_AGORA_CERTIFICATE',   // 必填
    
    // 录制配置 (可选)
    'agora_customer_id'          => 'YOUR_CUSTOMER_ID',
    'agora_customer_certificate' => 'YOUR_CUSTOMER_CERTIFICATE',
];
```

### 3. 服务开通

在声网控制台开通以下服务：
- **实时音视频 (RTC)**: 基础通话功能
- **云端录制**: 通话录制功能
- **实时消息 (RTM)**: 信令传输 (可选)

## 腾讯云IM配置

### 1. 开通IM服务

1. 访问 [腾讯云IM控制台](https://console.cloud.tencent.com/im)
2. 创建IM应用
3. 获取 SDKAppID 和密钥

### 2. 配置参数

```php
// 腾讯IM配置
$config = [
    'tencent_sdkappid'      => 'YOUR_SDKAPPID',        // 应用ID
    'tencent_identifier'    => 'administrator',        // 管理员账号
    'tencent_sha_key'       => 'YOUR_SECRET_KEY',      // 密钥
    'tencent_group_id'      => '@FGC#FULLGROUPIDKEY',  // 全局群组ID
];
```

### 3. 创建管理员账号

```bash
# 使用IM控制台或API创建管理员账号
curl -X POST 'https://console.tim.qq.com/v4/im_open_login_svc/account_import' \
-H 'Content-Type: application/json' \
-d '{
    "UserID": "administrator",
    "Nick": "系统管理员",
    "FaceUrl": ""
}'
```

### 4. 创建全局群组

```php
// 创建全员广播大群
$result = create_broadcast_group('@FGC#FULLGROUPIDKEY', 'FullGroup', '全员广播群组');
```

## 腾讯云COS配置

### 1. 开通COS服务

1. 访问 [腾讯云COS控制台](https://console.cloud.tencent.com/cos)
2. 创建存储桶
3. 配置访问权限

### 2. 配置参数

```php
// 腾讯云COS配置
$config = [
    'tencent_cos' => [
        'secret_id'     => 'YOUR_SECRET_ID',
        'secret_key'    => 'YOUR_SECRET_KEY',
        'region'        => 'ap-shanghai',           // 地域
        'bucket'        => 'your-bucket-name',      // 存储桶名称
        'domain'        => 'https://your-domain',   // 自定义域名
    ]
];
```

## 数据库配置

### 1. 创建数据库表

```sql
-- 通话记录表
CREATE TABLE `la_video_call_record` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '发起用户ID',
  `call_be_user_id` int(11) NOT NULL COMMENT '被叫用户ID',
  `channel_id` varchar(100) NOT NULL COMMENT '通话频道ID',
  `status` tinyint(1) DEFAULT '0' COMMENT '通话状态 0=发起 1=接通 2=挂断',
  `type` tinyint(1) DEFAULT '1' COMMENT '通话类型 1=视频 2=语音',
  `create_time` int(11) NOT NULL COMMENT '创建时间',
  `update_time` int(11) DEFAULT NULL COMMENT '更新时间',
  `duration` int(11) DEFAULT '0' COMMENT '通话时长(秒)',
  `charge_amount` decimal(10,2) DEFAULT '0.00' COMMENT '扣费金额',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_call_be_user_id` (`call_be_user_id`),
  KEY `idx_channel_id` (`channel_id`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='视频通话记录表';

-- 通话质量统计表
CREATE TABLE `la_call_quality_stats` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `channel_id` varchar(100) NOT NULL COMMENT '频道ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `network_quality` tinyint(1) DEFAULT '0' COMMENT '网络质量 1-6',
  `video_bitrate` int(11) DEFAULT '0' COMMENT '视频码率',
  `audio_bitrate` int(11) DEFAULT '0' COMMENT '音频码率',
  `packet_loss_rate` decimal(5,2) DEFAULT '0.00' COMMENT '丢包率',
  `create_time` int(11) NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_channel_id` (`channel_id`),
  KEY `idx_user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='通话质量统计表';
```

### 2. 索引优化

```sql
-- 添加复合索引
ALTER TABLE `la_video_call_record` ADD INDEX `idx_user_status_time` (`user_id`, `status`, `create_time`);
ALTER TABLE `la_video_call_record` ADD INDEX `idx_channel_status` (`channel_id`, `status`);

-- 分区表 (可选，适用于大数据量)
ALTER TABLE `la_video_call_record` 
PARTITION BY RANGE (create_time) (
    PARTITION p202401 VALUES LESS THAN (UNIX_TIMESTAMP('2024-02-01')),
    PARTITION p202402 VALUES LESS THAN (UNIX_TIMESTAMP('2024-03-01')),
    PARTITION p202403 VALUES LESS THAN (UNIX_TIMESTAMP('2024-04-01'))
);
```

## Nginx配置

### 1. 基础配置

```nginx
server {
    listen 443 ssl http2;
    server_name your-domain.com;
    
    # SSL证书配置
    ssl_certificate /path/to/your/cert.pem;
    ssl_certificate_key /path/to/your/key.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE:ECDH:AES:HIGH:!NULL:!aNULL:!MD5:!ADH:!RC4;
    
    root /var/www/html;
    index index.php index.html;
    
    # 文件上传大小限制
    client_max_body_size 100M;
    
    # PHP处理
    location ~ \.php$ {
        fastcgi_pass 127.0.0.1:9000;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;
        
        # 增加超时时间
        fastcgi_read_timeout 300;
        fastcgi_send_timeout 300;
    }
    
    # 静态资源缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # WebSocket支持 (如需要)
    location /ws {
        proxy_pass http://127.0.0.1:8080;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
    }
}

# HTTP重定向到HTTPS
server {
    listen 80;
    server_name your-domain.com;
    return 301 https://$server_name$request_uri;
}
```

### 2. 负载均衡配置

```nginx
upstream backend {
    server 127.0.0.1:8001 weight=3;
    server 127.0.0.1:8002 weight=2;
    server 127.0.0.1:8003 weight=1;
    
    # 健康检查
    keepalive 32;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com;
    
    location / {
        proxy_pass http://backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

## Redis配置

### 1. 基础配置

```bash
# redis.conf
bind 127.0.0.1
port 6379
timeout 300
keepalive 60

# 内存配置
maxmemory 2gb
maxmemory-policy allkeys-lru

# 持久化配置
save 900 1
save 300 10
save 60 10000

# 日志配置
loglevel notice
logfile /var/log/redis/redis-server.log
```

### 2. 缓存策略

```php
// 缓存配置
$cache_config = [
    // 用户信息缓存 (30分钟)
    'user_info_ttl' => 1800,
    
    // Token缓存 (24小时)
    'agora_token_ttl' => 86400,
    
    // 配置缓存 (1小时)
    'config_ttl' => 3600,
    
    // 通话状态缓存 (5分钟)
    'call_status_ttl' => 300,
];
```

## 监控和日志

### 1. 日志配置

```php
// 日志配置
$log_config = [
    'video_call' => [
        'path' => '/var/log/app/video_call.log',
        'level' => 'info',
        'max_files' => 30,
    ],
    'im_message' => [
        'path' => '/var/log/app/im_message.log',
        'level' => 'info',
        'max_files' => 30,
    ],
    'error' => [
        'path' => '/var/log/app/error.log',
        'level' => 'error',
        'max_files' => 90,
    ],
];
```

### 2. 监控指标

```bash
# 系统监控脚本
#!/bin/bash

# 检查服务状态
systemctl status nginx
systemctl status php-fpm
systemctl status mysql
systemctl status redis

# 检查端口监听
netstat -tlnp | grep :80
netstat -tlnp | grep :443
netstat -tlnp | grep :3306
netstat -tlnp | grep :6379

# 检查磁盘空间
df -h

# 检查内存使用
free -h

# 检查CPU负载
uptime
```

## 安全配置

### 1. 防火墙配置

```bash
# 开放必要端口
firewall-cmd --permanent --add-port=80/tcp
firewall-cmd --permanent --add-port=443/tcp
firewall-cmd --permanent --add-port=22/tcp

# 限制数据库访问
firewall-cmd --permanent --add-rich-rule="rule family='ipv4' source address='127.0.0.1' port protocol='tcp' port='3306' accept"

# 重载配置
firewall-cmd --reload
```

### 2. 应用安全

```php
// 安全配置
$security_config = [
    // API访问频率限制
    'api_rate_limit' => [
        'video_call' => 10,     // 每分钟最多10次通话请求
        'message' => 100,       // 每分钟最多100条消息
    ],
    
    // IP白名单 (管理后台)
    'admin_ip_whitelist' => [
        '127.0.0.1',
        '***********/24',
    ],
    
    // Token有效期
    'token_expire' => [
        'agora' => 86400 * 30,  // 30天
        'api' => 7200,          // 2小时
    ],
];
```

## 性能优化

### 1. PHP优化

```ini
; php.ini
memory_limit = 512M
max_execution_time = 300
max_input_time = 300
post_max_size = 100M
upload_max_filesize = 100M

; OPcache配置
opcache.enable=1
opcache.memory_consumption=256
opcache.interned_strings_buffer=16
opcache.max_accelerated_files=10000
opcache.validate_timestamps=0
```

### 2. MySQL优化

```ini
# my.cnf
[mysqld]
innodb_buffer_pool_size = 2G
innodb_log_file_size = 256M
innodb_flush_log_at_trx_commit = 2
innodb_flush_method = O_DIRECT

query_cache_type = 1
query_cache_size = 256M

max_connections = 1000
thread_cache_size = 50
```

## 部署检查清单

### 1. 服务检查

- [ ] Nginx/Apache 正常运行
- [ ] PHP-FPM 正常运行
- [ ] MySQL 正常运行
- [ ] Redis 正常运行
- [ ] SSL证书有效

### 2. 配置检查

- [ ] 声网配置正确
- [ ] 腾讯IM配置正确
- [ ] 腾讯COS配置正确
- [ ] 数据库连接正常
- [ ] Redis连接正常

### 3. 功能测试

- [ ] 用户注册登录正常
- [ ] 视频通话功能正常
- [ ] 语音通话功能正常
- [ ] IM消息发送正常
- [ ] 录制功能正常
- [ ] 管理后台监控正常

### 4. 性能测试

- [ ] 并发通话测试
- [ ] 网络质量测试
- [ ] 服务器负载测试
- [ ] 数据库性能测试

---

**注意**: 
1. 生产环境必须使用HTTPS，否则无法获取摄像头和麦克风权限
2. 定期备份数据库和配置文件
3. 监控服务器资源使用情况，及时扩容
4. 定期更新SSL证书，避免过期
